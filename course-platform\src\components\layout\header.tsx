'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Menu, X, User, LogOut, Settings, BookOpen, Gift } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useAuthStore } from '@/store/auth'
import { useSiteStore } from '@/store/site'
import { createClient } from '@/lib/supabase/client'

interface HeaderProps {
  locale: string
}

export function Header({ locale }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const pathname = usePathname()
  const { user, appUser, logout } = useAuthStore()
  const { getSettingValue } = useSiteStore()
  const supabase = createClient()

  const isRTL = locale === 'ar'
  const siteName = getSettingValue('site_name', locale as 'ar' | 'en')

  const navigation = [
    {
      name: locale === 'ar' ? 'الرئيسية' : 'Home',
      href: `/${locale}`,
    },
    {
      name: locale === 'ar' ? 'الكورسات' : 'Courses',
      href: `/${locale}/courses`,
    },
    {
      name: locale === 'ar' ? 'التصنيفات' : 'Categories',
      href: `/${locale}/categories`,
    },
    {
      name: locale === 'ar' ? 'جمع النقاط' : 'Earn Points',
      href: `/${locale}/earn-points`,
    },
  ]

  const handleLogout = async () => {
    await supabase.auth.signOut()
    logout()
  }

  return (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href={`/${locale}`} className="flex items-center">
              <BookOpen className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">
                {siteName || 'Course Platform'}
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  pathname === item.href
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-4">
            {user ? (
              <>
                <div className="flex items-center space-x-2 text-sm text-gray-700">
                  <Gift className="h-4 w-4" />
                  <span>
                    {locale === 'ar' 
                      ? `${appUser?.points || 0} نقطة`
                      : `${appUser?.points || 0} points`
                    }
                  </span>
                </div>
                <div className="relative group">
                  <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span>{appUser?.username}</span>
                  </Button>
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                    <Link
                      href={`/${locale}/profile`}
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      {locale === 'ar' ? 'الملف الشخصي' : 'Profile'}
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      {locale === 'ar' ? 'تسجيل الخروج' : 'Logout'}
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex items-center space-x-2">
                <Link href={`/${locale}/login`}>
                  <Button variant="ghost" size="sm">
                    {locale === 'ar' ? 'تسجيل الدخول' : 'Login'}
                  </Button>
                </Link>
                <Link href={`/${locale}/register`}>
                  <Button size="sm">
                    {locale === 'ar' ? 'إنشاء حساب' : 'Register'}
                  </Button>
                </Link>
              </div>
            )}

            {/* Language Switcher */}
            <div className="flex items-center space-x-1">
              <Link
                href={pathname.replace(`/${locale}`, '/ar')}
                className={`px-2 py-1 text-xs rounded ${
                  locale === 'ar' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                العربية
              </Link>
              <Link
                href={pathname.replace(`/${locale}`, '/en')}
                className={`px-2 py-1 text-xs rounded ${
                  locale === 'en' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                English
              </Link>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`block px-3 py-2 rounded-md text-base font-medium ${
                    pathname === item.href
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              
              {user ? (
                <div className="border-t pt-4 mt-4">
                  <div className="flex items-center px-3 py-2">
                    <Gift className="h-4 w-4 mr-2" />
                    <span className="text-sm text-gray-700">
                      {locale === 'ar' 
                        ? `${appUser?.points || 0} نقطة`
                        : `${appUser?.points || 0} points`
                      }
                    </span>
                  </div>
                  <Link
                    href={`/${locale}/profile`}
                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {locale === 'ar' ? 'الملف الشخصي' : 'Profile'}
                  </Link>
                  <button
                    onClick={() => {
                      handleLogout()
                      setIsMenuOpen(false)
                    }}
                    className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                  >
                    {locale === 'ar' ? 'تسجيل الخروج' : 'Logout'}
                  </button>
                </div>
              ) : (
                <div className="border-t pt-4 mt-4 space-y-1">
                  <Link
                    href={`/${locale}/login`}
                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {locale === 'ar' ? 'تسجيل الدخول' : 'Login'}
                  </Link>
                  <Link
                    href={`/${locale}/register`}
                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {locale === 'ar' ? 'إنشاء حساب' : 'Register'}
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
