'use client'

import { useState, useEffect } from 'react'
import { AlertCircle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { OfferCard } from './offer-card'
import { useAuthStore } from '@/store/auth'
import { OGAdsOffer, Language } from '@/types'

interface OffersListProps {
  locale: string
}

export function OffersList({ locale }: OffersListProps) {
  const [offers, setOffers] = useState<(OGAdsOffer & { trackingUrl?: string; estimatedPoints?: number })[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { user } = useAuthStore()

  const fetchOffers = async () => {
    if (!user) {
      setError(locale === 'ar' ? 'يجب تسجيل الدخول لعرض العروض' : 'Please login to view offers')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/offers?max=20')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch offers')
      }

      if (data.success) {
        setOffers(data.offers || [])
      } else {
        throw new Error(data.error || 'Failed to fetch offers')
      }
    } catch (error) {
      console.error('Error fetching offers:', error)
      setError(
        locale === 'ar' 
          ? 'حدث خطأ في تحميل العروض. يرجى المحاولة مرة أخرى.'
          : 'Error loading offers. Please try again.'
      )
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOffers()
  }, [user, locale])

  const handleOfferClick = (offer: OGAdsOffer) => {
    // Track offer click
    console.log('Offer clicked:', offer.offerid)
    
    // You can add analytics tracking here
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'offer_click', {
        offer_id: offer.offerid,
        offer_name: offer.name_short,
        payout: offer.payout,
      })
    }
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">
          {locale === 'ar' ? 'جاري تحميل العروض...' : 'Loading offers...'}
        </p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchOffers} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          {locale === 'ar' ? 'إعادة المحاولة' : 'Try Again'}
        </Button>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
        <p className="text-gray-600 mb-4">
          {locale === 'ar' 
            ? 'يجب تسجيل الدخول لعرض العروض المتاحة'
            : 'Please login to view available offers'
          }
        </p>
        <Button asChild>
          <a href={`/${locale}/login`}>
            {locale === 'ar' ? 'تسجيل الدخول' : 'Login'}
          </a>
        </Button>
      </div>
    )
  }

  if (offers.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600 mb-4">
          {locale === 'ar' 
            ? 'لا توجد عروض متاحة في الوقت الحالي'
            : 'No offers available at the moment'
          }
        </p>
        <Button onClick={fetchOffers} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          {locale === 'ar' ? 'تحديث' : 'Refresh'}
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Refresh Button */}
      <div className="flex justify-between items-center">
        <p className="text-gray-600">
          {locale === 'ar' 
            ? `${offers.length} عرض متاح`
            : `${offers.length} offers available`
          }
        </p>
        <Button onClick={fetchOffers} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          {locale === 'ar' ? 'تحديث' : 'Refresh'}
        </Button>
      </div>

      {/* Offers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {offers.map((offer) => (
          <OfferCard
            key={offer.offerid}
            offer={offer}
            locale={locale as Language}
            onOfferClick={handleOfferClick}
          />
        ))}
      </div>

      {/* Load More Button (if needed) */}
      {offers.length >= 20 && (
        <div className="text-center pt-8">
          <Button onClick={fetchOffers} variant="outline">
            {locale === 'ar' ? 'تحميل المزيد' : 'Load More'}
          </Button>
        </div>
      )}

      {/* Disclaimer */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-8">
        <p className="text-sm text-yellow-800">
          {locale === 'ar' 
            ? '⚠️ تنبيه: تأكد من قراءة شروط كل عرض بعناية قبل المشاركة. النقاط تُضاف تلقائياً عند إكمال العرض بنجاح.'
            : '⚠️ Disclaimer: Make sure to read each offer\'s terms carefully before participating. Points are automatically added upon successful completion.'
          }
        </p>
      </div>
    </div>
  )
}
