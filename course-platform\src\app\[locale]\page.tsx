import { Metadata } from 'next'
import Link from 'next/link'
import { <PERSON><PERSON><PERSON>, Users, Award, TrendingUp, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { CourseCard } from '@/components/courses/course-card'
import { AdSpace } from '@/components/ads/ad-space'
import { createClient } from '@/lib/supabase/server'
import { Course, Category, Language } from '@/types'

interface HomePageProps {
  params: { locale: string }
}

export async function generateMetadata({ params }: HomePageProps): Promise<Metadata> {
  const { locale } = params
  const supabase = await createClient()
  
  const { data: settings } = await supabase
    .from('site_settings')
    .select('*')
    .in('key', ['site_name', 'site_description', 'site_keywords'])

  const siteName = settings?.find(s => s.key === 'site_name')
  const siteDescription = settings?.find(s => s.key === 'site_description')
  const siteKeywords = settings?.find(s => s.key === 'site_keywords')

  const title = locale === 'ar' 
    ? siteName?.value_ar || 'منصة الكورسات'
    : siteName?.value_en || 'Course Platform'
  
  const description = locale === 'ar'
    ? siteDescription?.value_ar || 'منصة تعليمية متقدمة لتعلم المهارات الجديدة'
    : siteDescription?.value_en || 'Advanced learning platform for new skills'

  const keywords = locale === 'ar'
    ? siteKeywords?.value_ar || 'كورسات, تعليم, مهارات'
    : siteKeywords?.value_en || 'courses, education, skills'

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
    },
    alternates: {
      languages: {
        'ar': `/ar`,
        'en': `/en`,
      },
    },
  }
}

export default async function HomePage({ params }: HomePageProps) {
  const { locale } = params
  const supabase = await createClient()

  // Fetch featured courses
  const { data: courses } = await supabase
    .from('courses')
    .select(`
      *,
      category:categories(*),
      videos:course_videos(*)
    `)
    .limit(6)
    .order('created_at', { ascending: false })

  // Fetch categories
  const { data: categories } = await supabase
    .from('categories')
    .select('*')
    .limit(6)

  const stats = [
    {
      icon: BookOpen,
      value: '500+',
      label: locale === 'ar' ? 'كورس متاح' : 'Available Courses',
    },
    {
      icon: Users,
      value: '10K+',
      label: locale === 'ar' ? 'طالب نشط' : 'Active Students',
    },
    {
      icon: Award,
      value: '95%',
      label: locale === 'ar' ? 'معدل الرضا' : 'Satisfaction Rate',
    },
    {
      icon: TrendingUp,
      value: '24/7',
      label: locale === 'ar' ? 'دعم متواصل' : 'Support Available',
    },
  ]

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {locale === 'ar' 
                ? 'تعلم مهارات جديدة واكسب نقاط'
                : 'Learn New Skills & Earn Points'
              }
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
              {locale === 'ar'
                ? 'منصة تعليمية متقدمة تجمع بين التعلم وكسب النقاط من خلال إكمال العروض والمهام'
                : 'Advanced learning platform that combines education with earning points through completing offers and tasks'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href={`/${locale}/courses`}>
                <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                  {locale === 'ar' ? 'تصفح الكورسات' : 'Browse Courses'}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href={`/${locale}/earn-points`}>
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                  {locale === 'ar' ? 'ابدأ كسب النقاط' : 'Start Earning Points'}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <div key={index} className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                    <Icon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Content Top Ads */}
      <AdSpace position="content-top" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" />

      {/* Featured Courses */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'ar' ? 'الكورسات المميزة' : 'Featured Courses'}
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              {locale === 'ar'
                ? 'اكتشف أحدث الكورسات المتاحة وابدأ رحلة التعلم اليوم'
                : 'Discover the latest available courses and start your learning journey today'
              }
            </p>
          </div>

          {courses && courses.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {courses.map((course) => (
                <CourseCard
                  key={course.id}
                  course={course as Course}
                  locale={locale as Language}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">
                {locale === 'ar' ? 'لا توجد كورسات متاحة حالياً' : 'No courses available at the moment'}
              </p>
            </div>
          )}

          <div className="text-center mt-12">
            <Link href={`/${locale}/courses`}>
              <Button size="lg">
                {locale === 'ar' ? 'عرض جميع الكورسات' : 'View All Courses'}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Between Courses Ads */}
      <AdSpace position="between-courses" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" />

      {/* Categories Section */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'ar' ? 'تصفح حسب التصنيف' : 'Browse by Category'}
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              {locale === 'ar'
                ? 'اختر التصنيف الذي يناسب اهتماماتك وابدأ التعلم'
                : 'Choose the category that matches your interests and start learning'
              }
            </p>
          </div>

          {categories && categories.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
              {categories.map((category) => {
                const categoryName = locale === 'ar' ? category.name_ar : category.name_en
                const categorySlug = locale === 'ar' ? category.slug_ar : category.slug_en
                
                return (
                  <Link
                    key={category.id}
                    href={`/${locale}/categories/${categorySlug}`}
                    className="group"
                  >
                    <Card className="text-center hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                      <CardContent className="p-6">
                        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-white text-xl font-bold">
                            {categoryName.charAt(0)}
                          </span>
                        </div>
                        <h3 className="font-semibold text-sm group-hover:text-blue-600 transition-colors">
                          {categoryName}
                        </h3>
                      </CardContent>
                    </Card>
                  </Link>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">
                {locale === 'ar' ? 'لا توجد تصنيفات متاحة حالياً' : 'No categories available at the moment'}
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Content Bottom Ads */}
      <AdSpace position="content-bottom" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" />

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            {locale === 'ar' 
              ? 'ابدأ رحلة التعلم اليوم'
              : 'Start Your Learning Journey Today'
            }
          </h2>
          <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
            {locale === 'ar'
              ? 'انضم إلى آلاف الطلاب واكسب نقاط أثناء تعلم مهارات جديدة'
              : 'Join thousands of students and earn points while learning new skills'
            }
          </p>
          <Link href={`/${locale}/register`}>
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              {locale === 'ar' ? 'إنشاء حساب مجاني' : 'Create Free Account'}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>
    </div>
  )
}
