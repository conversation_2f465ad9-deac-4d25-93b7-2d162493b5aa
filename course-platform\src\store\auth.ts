import { create } from 'zustand'
import { User } from '@supabase/supabase-js'
import { User as AppUser } from '@/types'

interface AuthState {
  user: User | null
  appUser: AppUser | null
  isLoading: boolean
  setUser: (user: User | null) => void
  setAppUser: (appUser: AppUser | null) => void
  setLoading: (loading: boolean) => void
  logout: () => void
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  appUser: null,
  isLoading: true,
  setUser: (user) => set({ user }),
  setAppUser: (appUser) => set({ appUser }),
  setLoading: (isLoading) => set({ isLoading }),
  logout: () => set({ user: null, appUser: null }),
}))
