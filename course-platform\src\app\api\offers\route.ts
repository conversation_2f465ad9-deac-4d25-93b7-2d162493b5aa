import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { ogadsService } from '@/lib/ogads'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get client IP and user agent
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               '127.0.0.1'
    const userAgent = request.headers.get('user-agent') || ''

    // Get query parameters
    const searchParams = request.nextUrl.searchParams
    const ctype = searchParams.get('ctype') ? parseInt(searchParams.get('ctype')!) : undefined
    const max = searchParams.get('max') ? parseInt(searchParams.get('max')!) : 10
    const country = searchParams.get('country') || undefined

    // Fetch offers from OGAds
    const offersResponse = await ogadsService.getOffers({
      ip,
      userAgent,
      ctype,
      max,
      country
    })

    if (!offersResponse.success) {
      return NextResponse.json({ 
        error: offersResponse.error || 'Failed to fetch offers' 
      }, { status: 500 })
    }

    // Add tracking URLs for each offer
    const offersWithTracking = offersResponse.offers.map(offer => ({
      ...offer,
      trackingUrl: ogadsService.getOfferTrackingUrl(offer, user.id),
      estimatedPoints: ogadsService.calculatePoints(parseFloat(offer.payout))
    }))

    return NextResponse.json({
      success: true,
      offers: offersWithTracking
    })

  } catch (error) {
    console.error('Error in offers API:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}
