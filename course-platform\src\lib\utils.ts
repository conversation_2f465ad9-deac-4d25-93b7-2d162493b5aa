import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date, locale: string = 'ar') {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (locale === 'ar') {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(dateObj)
  }
  
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(dateObj)
}

export function formatPoints(points: number, locale: string = 'ar') {
  if (locale === 'ar') {
    return `${points.toLocaleString('ar-SA')} نقطة`
  }
  return `${points.toLocaleString('en-US')} points`
}

export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

export function extractVideoId(url: string, type: 'youtube' | 'vimeo'): string | null {
  if (type === 'youtube') {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const match = url.match(regex)
    return match ? match[1] : null
  }
  
  if (type === 'vimeo') {
    const regex = /(?:vimeo\.com\/)([0-9]+)/
    const match = url.match(regex)
    return match ? match[1] : null
  }
  
  return null
}

export function getVideoEmbedUrl(url: string, type: 'youtube' | 'vimeo' | 'drive' | 'direct'): string {
  switch (type) {
    case 'youtube':
      const youtubeId = extractVideoId(url, 'youtube')
      return youtubeId ? `https://www.youtube.com/embed/${youtubeId}` : url
    
    case 'vimeo':
      const vimeoId = extractVideoId(url, 'vimeo')
      return vimeoId ? `https://player.vimeo.com/video/${vimeoId}` : url
    
    case 'drive':
      // Convert Google Drive share link to embed link
      const driveRegex = /\/file\/d\/([a-zA-Z0-9-_]+)/
      const driveMatch = url.match(driveRegex)
      return driveMatch ? `https://drive.google.com/file/d/${driveMatch[1]}/preview` : url
    
    case 'direct':
    default:
      return url
  }
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength).trim() + '...'
}

export function isRTL(locale: string): boolean {
  return locale === 'ar'
}
