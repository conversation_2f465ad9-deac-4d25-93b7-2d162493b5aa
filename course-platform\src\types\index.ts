export interface OGAdsOffer {
  offerid: string
  name: string
  name_short: string
  description: string
  adcopy: string
  picture: string
  payout: string
  country: string
  device: string
  link: string
  epc: string
}

export interface OGAdsResponse {
  success: boolean
  error: string | null
  offers: OGAdsOffer[]
}

export interface Course {
  id: string
  title_ar: string
  title_en: string
  slug_ar: string
  slug_en: string
  description_ar: string
  description_en: string
  content_ar: string
  content_en: string
  image_url: string | null
  category_id: string
  is_free: boolean
  required_points: number
  meta_title_ar: string | null
  meta_title_en: string | null
  meta_description_ar: string | null
  meta_description_en: string | null
  keywords_ar: string | null
  keywords_en: string | null
  created_at: string
  updated_at: string
  category?: Category
  videos?: CourseVideo[]
}

export interface Category {
  id: string
  name_ar: string
  name_en: string
  slug_ar: string
  slug_en: string
  description_ar: string | null
  description_en: string | null
  image_url: string | null
  created_at: string
  updated_at: string
}

export interface CourseVideo {
  id: string
  course_id: string
  title_ar: string
  title_en: string
  video_url: string
  video_type: 'youtube' | 'vimeo' | 'drive' | 'direct'
  duration: number | null
  order_index: number
  is_free: boolean
  created_at: string
  updated_at: string
}

export interface User {
  id: string
  email: string
  username: string
  points: number
  created_at: string
  updated_at: string
}

export interface Admin {
  id: string
  username: string
  email: string
  created_at: string
  updated_at: string
}

export interface SiteSetting {
  id: string
  key: string
  value_ar: string | null
  value_en: string | null
  type: 'text' | 'textarea' | 'number' | 'boolean' | 'json'
  created_at: string
  updated_at: string
}

export interface AdsSetting {
  id: string
  name: string
  position: string
  ad_code: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export type Language = 'ar' | 'en'

export interface LocalizedContent {
  ar: string
  en: string
}

export interface SEOData {
  title: LocalizedContent
  description: LocalizedContent
  keywords: LocalizedContent
  image?: string
}
