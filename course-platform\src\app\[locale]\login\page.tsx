import { Metadata } from 'next'
import { LoginForm } from '@/components/auth/login-form'

interface LoginPageProps {
  params: { locale: string }
}

export async function generateMetadata({ params }: LoginPageProps): Promise<Metadata> {
  const { locale } = params
  
  const title = locale === 'ar' ? 'تسجيل الدخول' : 'Login'
  const description = locale === 'ar'
    ? 'سجل دخولك للوصول إلى حسابك والكورسات'
    : 'Login to access your account and courses'

  return {
    title,
    description,
  }
}

export default function LoginPage({ params }: LoginPageProps) {
  const { locale } = params

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {locale === 'ar' ? 'تسجيل الدخول' : 'Sign in to your account'}
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {locale === 'ar' ? 'أو' : 'Or'}{' '}
            <a
              href={`/${locale}/register`}
              className="font-medium text-blue-600 hover:text-blue-500"
            >
              {locale === 'ar' ? 'إنشاء حساب جديد' : 'create a new account'}
            </a>
          </p>
        </div>
        <LoginForm locale={locale} />
      </div>
    </div>
  )
}
