'use client'

import { useEffect } from 'react'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { AdSpace } from '@/components/ads/ad-space'
import { useAuthStore } from '@/store/auth'
import { useSiteStore } from '@/store/site'
import { createClient } from '@/lib/supabase/client'
import { isRTL } from '@/lib/utils'

interface LocaleLayoutProps {
  children: React.ReactNode
  params: { locale: string }
}

export default function LocaleLayout({ children, params }: LocaleLayoutProps) {
  const { locale } = params
  const { setUser, setAppUser, setLoading } = useAuthStore()
  const { setLanguage, setSettings, setAdsSettings, setLoading: setSiteLoading } = useSiteStore()
  const supabase = createClient()

  useEffect(() => {
    // Set language
    setLanguage(locale as 'ar' | 'en')

    // Load site settings
    const loadSiteSettings = async () => {
      try {
        const { data: settings } = await supabase
          .from('site_settings')
          .select('*')
        
        const { data: adsSettings } = await supabase
          .from('ads_settings')
          .select('*')
          .eq('is_active', true)

        if (settings) setSettings(settings)
        if (adsSettings) setAdsSettings(adsSettings)
      } catch (error) {
        console.error('Error loading site settings:', error)
      } finally {
        setSiteLoading(false)
      }
    }

    loadSiteSettings()
  }, [locale, setLanguage, setSettings, setAdsSettings, setSiteLoading, supabase])

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        
        if (session?.user) {
          setUser(session.user)
          
          // Get app user data
          const { data: appUser } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single()
          
          if (appUser) {
            setAppUser(appUser)
          }
        }
      } catch (error) {
        console.error('Error getting initial session:', error)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          setUser(session.user)
          
          // Get or create app user data
          const { data: appUser, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single()
          
          if (error && error.code === 'PGRST116') {
            // User doesn't exist, create one
            const { data: newUser } = await supabase
              .from('users')
              .insert({
                id: session.user.id,
                email: session.user.email!,
                username: session.user.email!.split('@')[0],
                points: 0
              })
              .select()
              .single()
            
            if (newUser) {
              setAppUser(newUser)
            }
          } else if (appUser) {
            setAppUser(appUser)
          }
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
          setAppUser(null)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [setUser, setAppUser, setLoading, supabase])

  return (
    <div className={`min-h-screen flex flex-col ${isRTL(locale) ? 'rtl' : 'ltr'}`} dir={isRTL(locale) ? 'rtl' : 'ltr'}>
      {/* Header Ads */}
      <AdSpace position="header" />
      
      {/* Header */}
      <Header locale={locale} />
      
      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>
      
      {/* Footer */}
      <Footer locale={locale} />
      
      {/* Footer Ads */}
      <AdSpace position="footer" />
      
      {/* Google AdSense Script */}
      <script
        async
        src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"
        crossOrigin="anonymous"
      />
    </div>
  )
}
