/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/page";
exports.ids = ["app/[locale]/page"];
exports.modules = {

/***/ "(rsc)/./app/[locale]/layout.tsx":
/*!*********************************!*\
  !*** ./app/[locale]/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\33\\course-platform\\app\\[locale]\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/[locale]/page.tsx":
/*!*******************************!*\
  !*** ./app/[locale]/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,TrendingUp,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,TrendingUp,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,TrendingUp,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,TrendingUp,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,TrendingUp,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_courses_course_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/courses/course-card */ \"(rsc)/./src/components/courses/course-card.tsx\");\n/* harmony import */ var _components_ads_ad_space__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ads/ad-space */ \"(rsc)/./src/components/ads/ad-space.tsx\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\n\n\n\n\n\n\nasync function generateMetadata({ params }) {\n    const { locale } = params;\n    const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_6__.createClient)();\n    const { data: settings } = await supabase.from('site_settings').select('*').in('key', [\n        'site_name',\n        'site_description',\n        'site_keywords'\n    ]);\n    const siteName = settings?.find((s)=>s.key === 'site_name');\n    const siteDescription = settings?.find((s)=>s.key === 'site_description');\n    const siteKeywords = settings?.find((s)=>s.key === 'site_keywords');\n    const title = locale === 'ar' ? siteName?.value_ar || 'منصة الكورسات' : siteName?.value_en || 'Course Platform';\n    const description = locale === 'ar' ? siteDescription?.value_ar || 'منصة تعليمية متقدمة لتعلم المهارات الجديدة' : siteDescription?.value_en || 'Advanced learning platform for new skills';\n    const keywords = locale === 'ar' ? siteKeywords?.value_ar || 'كورسات, تعليم, مهارات' : siteKeywords?.value_en || 'courses, education, skills';\n    return {\n        title,\n        description,\n        keywords,\n        openGraph: {\n            title,\n            description,\n            type: 'website'\n        },\n        alternates: {\n            languages: {\n                'ar': `/ar`,\n                'en': `/en`\n            }\n        }\n    };\n}\nasync function HomePage({ params }) {\n    const { locale } = params;\n    const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_6__.createClient)();\n    // Fetch featured courses\n    const { data: courses } = await supabase.from('courses').select(`\n      *,\n      category:categories(*),\n      videos:course_videos(*)\n    `).limit(6).order('created_at', {\n        ascending: false\n    });\n    // Fetch categories\n    const { data: categories } = await supabase.from('categories').select('*').limit(6);\n    const stats = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BookOpen_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            value: '500+',\n            label: locale === 'ar' ? 'كورس متاح' : 'Available Courses'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BookOpen_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            value: '10K+',\n            label: locale === 'ar' ? 'طالب نشط' : 'Active Students'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BookOpen_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            value: '95%',\n            label: locale === 'ar' ? 'معدل الرضا' : 'Satisfaction Rate'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BookOpen_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            value: '24/7',\n            label: locale === 'ar' ? 'دعم متواصل' : 'Support Available'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                children: locale === 'ar' ? 'تعلم مهارات جديدة واكسب نقاط' : 'Learn New Skills & Earn Points'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto\",\n                                children: locale === 'ar' ? 'منصة تعليمية متقدمة تجمع بين التعلم وكسب النقاط من خلال إكمال العروض والمهام' : 'Advanced learning platform that combines education with earning points through completing offers and tasks'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: `/${locale}/courses`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-white text-blue-600 hover:bg-gray-100\",\n                                            children: [\n                                                locale === 'ar' ? 'تصفح الكورسات' : 'Browse Courses',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"ml-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: `/${locale}/earn-points`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"border-white text-white hover:bg-white hover:text-blue-600\",\n                                            children: locale === 'ar' ? 'ابدأ كسب النقاط' : 'Start Earning Points'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: stats.map((stat, index)=>{\n                            const Icon = stat.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_ad_space__WEBPACK_IMPORTED_MODULE_5__.AdSpace, {\n                position: \"content-top\",\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: locale === 'ar' ? 'الكورسات المميزة' : 'Featured Courses'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 max-w-2xl mx-auto\",\n                                    children: locale === 'ar' ? 'اكتشف أحدث الكورسات المتاحة وابدأ رحلة التعلم اليوم' : 'Discover the latest available courses and start your learning journey today'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        courses && courses.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: courses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_courses_course_card__WEBPACK_IMPORTED_MODULE_4__.CourseCard, {\n                                    course: course,\n                                    locale: locale\n                                }, course.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: locale === 'ar' ? 'لا توجد كورسات متاحة حالياً' : 'No courses available at the moment'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: `/${locale}/courses`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    children: [\n                                        locale === 'ar' ? 'عرض جميع الكورسات' : 'View All Courses',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_ad_space__WEBPACK_IMPORTED_MODULE_5__.AdSpace, {\n                position: \"between-courses\",\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: locale === 'ar' ? 'تصفح حسب التصنيف' : 'Browse by Category'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 max-w-2xl mx-auto\",\n                                    children: locale === 'ar' ? 'اختر التصنيف الذي يناسب اهتماماتك وابدأ التعلم' : 'Choose the category that matches your interests and start learning'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        categories && categories.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\",\n                            children: categories.map((category)=>{\n                                const categoryName = locale === 'ar' ? category.name_ar : category.name_en;\n                                const categorySlug = locale === 'ar' ? category.slug_ar : category.slug_en;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: `/${locale}/categories/${categorySlug}`,\n                                    className: \"group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"text-center hover:shadow-lg transition-all duration-300 group-hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-xl font-bold\",\n                                                        children: categoryName.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-sm group-hover:text-blue-600 transition-colors\",\n                                                    children: categoryName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 21\n                                    }, this)\n                                }, category.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: locale === 'ar' ? 'لا توجد تصنيفات متاحة حالياً' : 'No categories available at the moment'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_ad_space__WEBPACK_IMPORTED_MODULE_5__.AdSpace, {\n                position: \"content-bottom\",\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-blue-600 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-6\",\n                            children: locale === 'ar' ? 'ابدأ رحلة التعلم اليوم' : 'Start Your Learning Journey Today'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl mb-8 text-blue-100 max-w-2xl mx-auto\",\n                            children: locale === 'ar' ? 'انضم إلى آلاف الطلاب واكسب نقاط أثناء تعلم مهارات جديدة' : 'Join thousands of students and earn points while learning new skills'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: `/${locale}/register`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"lg\",\n                                className: \"bg-white text-blue-600 hover:bg-gray-100\",\n                                children: [\n                                    locale === 'ar' ? 'إنشاء حساب مجاني' : 'Create Free Account',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"ml-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW2xvY2FsZV0vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNEI7QUFDaUQ7QUFDOUI7QUFDZ0M7QUFDbEI7QUFDVjtBQUNDO0FBTzdDLGVBQWVZLGlCQUFpQixFQUFFQyxNQUFNLEVBQWlCO0lBQzlELE1BQU0sRUFBRUMsTUFBTSxFQUFFLEdBQUdEO0lBQ25CLE1BQU1FLFdBQVcsTUFBTUosa0VBQVlBO0lBRW5DLE1BQU0sRUFBRUssTUFBTUMsUUFBUSxFQUFFLEdBQUcsTUFBTUYsU0FDOUJHLElBQUksQ0FBQyxpQkFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxPQUFPO1FBQUM7UUFBYTtRQUFvQjtLQUFnQjtJQUUvRCxNQUFNQyxXQUFXSixVQUFVSyxLQUFLQyxDQUFBQSxJQUFLQSxFQUFFQyxHQUFHLEtBQUs7SUFDL0MsTUFBTUMsa0JBQWtCUixVQUFVSyxLQUFLQyxDQUFBQSxJQUFLQSxFQUFFQyxHQUFHLEtBQUs7SUFDdEQsTUFBTUUsZUFBZVQsVUFBVUssS0FBS0MsQ0FBQUEsSUFBS0EsRUFBRUMsR0FBRyxLQUFLO0lBRW5ELE1BQU1HLFFBQVFiLFdBQVcsT0FDckJPLFVBQVVPLFlBQVksa0JBQ3RCUCxVQUFVUSxZQUFZO0lBRTFCLE1BQU1DLGNBQWNoQixXQUFXLE9BQzNCVyxpQkFBaUJHLFlBQVksK0NBQzdCSCxpQkFBaUJJLFlBQVk7SUFFakMsTUFBTUUsV0FBV2pCLFdBQVcsT0FDeEJZLGNBQWNFLFlBQVksMEJBQzFCRixjQUFjRyxZQUFZO0lBRTlCLE9BQU87UUFDTEY7UUFDQUc7UUFDQUM7UUFDQUMsV0FBVztZQUNUTDtZQUNBRztZQUNBRyxNQUFNO1FBQ1I7UUFDQUMsWUFBWTtZQUNWQyxXQUFXO2dCQUNULE1BQU0sQ0FBQyxHQUFHLENBQUM7Z0JBQ1gsTUFBTSxDQUFDLEdBQUcsQ0FBQztZQUNiO1FBQ0Y7SUFDRjtBQUNGO0FBRWUsZUFBZUMsU0FBUyxFQUFFdkIsTUFBTSxFQUFpQjtJQUM5RCxNQUFNLEVBQUVDLE1BQU0sRUFBRSxHQUFHRDtJQUNuQixNQUFNRSxXQUFXLE1BQU1KLGtFQUFZQTtJQUVuQyx5QkFBeUI7SUFDekIsTUFBTSxFQUFFSyxNQUFNcUIsT0FBTyxFQUFFLEdBQUcsTUFBTXRCLFNBQzdCRyxJQUFJLENBQUMsV0FDTEMsTUFBTSxDQUFDLENBQUM7Ozs7SUFJVCxDQUFDLEVBQ0FtQixLQUFLLENBQUMsR0FDTkMsS0FBSyxDQUFDLGNBQWM7UUFBRUMsV0FBVztJQUFNO0lBRTFDLG1CQUFtQjtJQUNuQixNQUFNLEVBQUV4QixNQUFNeUIsVUFBVSxFQUFFLEdBQUcsTUFBTTFCLFNBQ2hDRyxJQUFJLENBQUMsY0FDTEMsTUFBTSxDQUFDLEtBQ1BtQixLQUFLLENBQUM7SUFFVCxNQUFNSSxRQUFRO1FBQ1o7WUFDRUMsTUFBTTFDLHNIQUFRQTtZQUNkMkMsT0FBTztZQUNQQyxPQUFPL0IsV0FBVyxPQUFPLGNBQWM7UUFDekM7UUFDQTtZQUNFNkIsTUFBTXpDLHNIQUFLQTtZQUNYMEMsT0FBTztZQUNQQyxPQUFPL0IsV0FBVyxPQUFPLGFBQWE7UUFDeEM7UUFDQTtZQUNFNkIsTUFBTXhDLHNIQUFLQTtZQUNYeUMsT0FBTztZQUNQQyxPQUFPL0IsV0FBVyxPQUFPLGVBQWU7UUFDMUM7UUFDQTtZQUNFNkIsTUFBTXZDLHVIQUFVQTtZQUNoQndDLE9BQU87WUFDUEMsT0FBTy9CLFdBQVcsT0FBTyxlQUFlO1FBQzFDO0tBQ0Q7SUFFRCxxQkFDRSw4REFBQ2dDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDQztnQkFBUUQsV0FBVTswQkFDakIsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNFO2dDQUFHRixXQUFVOzBDQUNYakMsV0FBVyxPQUNSLGlDQUNBOzs7Ozs7MENBR04sOERBQUNvQztnQ0FBRUgsV0FBVTswQ0FDVmpDLFdBQVcsT0FDUixpRkFDQTs7Ozs7OzBDQUdOLDhEQUFDZ0M7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDL0Msa0RBQUlBO3dDQUFDbUQsTUFBTSxDQUFDLENBQUMsRUFBRXJDLE9BQU8sUUFBUSxDQUFDO2tEQUM5Qiw0RUFBQ1IseURBQU1BOzRDQUFDOEMsTUFBSzs0Q0FBS0wsV0FBVTs7Z0RBQ3pCakMsV0FBVyxPQUFPLGtCQUFrQjs4REFDckMsOERBQUNULHVIQUFVQTtvREFBQzBDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUcxQiw4REFBQy9DLGtEQUFJQTt3Q0FBQ21ELE1BQU0sQ0FBQyxDQUFDLEVBQUVyQyxPQUFPLFlBQVksQ0FBQztrREFDbEMsNEVBQUNSLHlEQUFNQTs0Q0FBQzhDLE1BQUs7NENBQUtDLFNBQVE7NENBQVVOLFdBQVU7c0RBQzNDakMsV0FBVyxPQUFPLG9CQUFvQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVNuRCw4REFBQ2tDO2dCQUFRRCxXQUFVOzBCQUNqQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNaTCxNQUFNWSxHQUFHLENBQUMsQ0FBQ0MsTUFBTUM7NEJBQ2hCLE1BQU1DLE9BQU9GLEtBQUtaLElBQUk7NEJBQ3RCLHFCQUNFLDhEQUFDRztnQ0FBZ0JDLFdBQVU7O2tEQUN6Qiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNVOzRDQUFLVixXQUFVOzs7Ozs7Ozs7OztrREFFbEIsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUF5Q1EsS0FBS1gsS0FBSzs7Ozs7O2tEQUNsRSw4REFBQ0U7d0NBQUlDLFdBQVU7a0RBQWlCUSxLQUFLVixLQUFLOzs7Ozs7OytCQUxsQ1c7Ozs7O3dCQVFkOzs7Ozs7Ozs7Ozs7Ozs7OzBCQU1OLDhEQUFDOUMsNkRBQU9BO2dCQUFDZ0QsVUFBUztnQkFBY1gsV0FBVTs7Ozs7OzBCQUcxQyw4REFBQ0M7Z0JBQVFELFdBQVU7MEJBQ2pCLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ1k7b0NBQUdaLFdBQVU7OENBQ1hqQyxXQUFXLE9BQU8scUJBQXFCOzs7Ozs7OENBRTFDLDhEQUFDb0M7b0NBQUVILFdBQVU7OENBQ1ZqQyxXQUFXLE9BQ1Isd0RBQ0E7Ozs7Ozs7Ozs7Ozt3QkFLUHVCLFdBQVdBLFFBQVF1QixNQUFNLEdBQUcsa0JBQzNCLDhEQUFDZDs0QkFBSUMsV0FBVTtzQ0FDWlYsUUFBUWlCLEdBQUcsQ0FBQyxDQUFDTyx1QkFDWiw4REFBQ3BELHVFQUFVQTtvQ0FFVG9ELFFBQVFBO29DQUNSL0MsUUFBUUE7bUNBRkgrQyxPQUFPQyxFQUFFOzs7Ozs7Ozs7aURBT3BCLDhEQUFDaEI7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNHO2dDQUFFSCxXQUFVOzBDQUNWakMsV0FBVyxPQUFPLGdDQUFnQzs7Ozs7Ozs7Ozs7c0NBS3pELDhEQUFDZ0M7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUMvQyxrREFBSUE7Z0NBQUNtRCxNQUFNLENBQUMsQ0FBQyxFQUFFckMsT0FBTyxRQUFRLENBQUM7MENBQzlCLDRFQUFDUix5REFBTUE7b0NBQUM4QyxNQUFLOzt3Q0FDVnRDLFdBQVcsT0FBTyxzQkFBc0I7c0RBQ3pDLDhEQUFDVCx1SEFBVUE7NENBQUMwQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUWhDLDhEQUFDckMsNkRBQU9BO2dCQUFDZ0QsVUFBUztnQkFBa0JYLFdBQVU7Ozs7OzswQkFHOUMsOERBQUNDO2dCQUFRRCxXQUFVOzBCQUNqQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNZO29DQUFHWixXQUFVOzhDQUNYakMsV0FBVyxPQUFPLHFCQUFxQjs7Ozs7OzhDQUUxQyw4REFBQ29DO29DQUFFSCxXQUFVOzhDQUNWakMsV0FBVyxPQUNSLG1EQUNBOzs7Ozs7Ozs7Ozs7d0JBS1AyQixjQUFjQSxXQUFXbUIsTUFBTSxHQUFHLGtCQUNqQyw4REFBQ2Q7NEJBQUlDLFdBQVU7c0NBQ1pOLFdBQVdhLEdBQUcsQ0FBQyxDQUFDUztnQ0FDZixNQUFNQyxlQUFlbEQsV0FBVyxPQUFPaUQsU0FBU0UsT0FBTyxHQUFHRixTQUFTRyxPQUFPO2dDQUMxRSxNQUFNQyxlQUFlckQsV0FBVyxPQUFPaUQsU0FBU0ssT0FBTyxHQUFHTCxTQUFTTSxPQUFPO2dDQUUxRSxxQkFDRSw4REFBQ3JFLGtEQUFJQTtvQ0FFSG1ELE1BQU0sQ0FBQyxDQUFDLEVBQUVyQyxPQUFPLFlBQVksRUFBRXFELGNBQWM7b0NBQzdDcEIsV0FBVTs4Q0FFViw0RUFBQ3hDLHFEQUFJQTt3Q0FBQ3dDLFdBQVU7a0RBQ2QsNEVBQUN2Qyw0REFBV0E7NENBQUN1QyxXQUFVOzs4REFDckIsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDdUI7d0RBQUt2QixXQUFVO2tFQUNiaUIsYUFBYU8sTUFBTSxDQUFDOzs7Ozs7Ozs7Ozs4REFHekIsOERBQUNDO29EQUFHekIsV0FBVTs4REFDWGlCOzs7Ozs7Ozs7Ozs7Ozs7OzttQ0FaRkQsU0FBU0QsRUFBRTs7Ozs7NEJBa0J0Qjs7Ozs7aURBR0YsOERBQUNoQjs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0c7Z0NBQUVILFdBQVU7MENBQ1ZqQyxXQUFXLE9BQU8saUNBQWlDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVE5RCw4REFBQ0osNkRBQU9BO2dCQUFDZ0QsVUFBUztnQkFBaUJYLFdBQVU7Ozs7OzswQkFHN0MsOERBQUNDO2dCQUFRRCxXQUFVOzBCQUNqQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDWTs0QkFBR1osV0FBVTtzQ0FDWGpDLFdBQVcsT0FDUiwyQkFDQTs7Ozs7O3NDQUdOLDhEQUFDb0M7NEJBQUVILFdBQVU7c0NBQ1ZqQyxXQUFXLE9BQ1IsNERBQ0E7Ozs7OztzQ0FHTiw4REFBQ2Qsa0RBQUlBOzRCQUFDbUQsTUFBTSxDQUFDLENBQUMsRUFBRXJDLE9BQU8sU0FBUyxDQUFDO3NDQUMvQiw0RUFBQ1IseURBQU1BO2dDQUFDOEMsTUFBSztnQ0FBS0wsV0FBVTs7b0NBQ3pCakMsV0FBVyxPQUFPLHFCQUFxQjtrREFDeEMsOERBQUNULHVIQUFVQTt3Q0FBQzBDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPcEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbW9hbWVcXERlc2t0b3BcXDMzXFxjb3Vyc2UtcGxhdGZvcm1cXGFwcFxcW2xvY2FsZV1cXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IEJvb2tPcGVuLCBVc2VycywgQXdhcmQsIFRyZW5kaW5nVXAsIEFycm93UmlnaHQgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJ1xuaW1wb3J0IHsgQ291cnNlQ2FyZCB9IGZyb20gJ0AvY29tcG9uZW50cy9jb3Vyc2VzL2NvdXJzZS1jYXJkJ1xuaW1wb3J0IHsgQWRTcGFjZSB9IGZyb20gJ0AvY29tcG9uZW50cy9hZHMvYWQtc3BhY2UnXG5pbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAL2xpYi9zdXBhYmFzZS9zZXJ2ZXInXG5pbXBvcnQgeyBDb3Vyc2UsIENhdGVnb3J5LCBMYW5ndWFnZSB9IGZyb20gJ0AvdHlwZXMnXG5cbmludGVyZmFjZSBIb21lUGFnZVByb3BzIHtcbiAgcGFyYW1zOiB7IGxvY2FsZTogc3RyaW5nIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdlbmVyYXRlTWV0YWRhdGEoeyBwYXJhbXMgfTogSG9tZVBhZ2VQcm9wcyk6IFByb21pc2U8TWV0YWRhdGE+IHtcbiAgY29uc3QgeyBsb2NhbGUgfSA9IHBhcmFtc1xuICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpXG4gIFxuICBjb25zdCB7IGRhdGE6IHNldHRpbmdzIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgIC5mcm9tKCdzaXRlX3NldHRpbmdzJylcbiAgICAuc2VsZWN0KCcqJylcbiAgICAuaW4oJ2tleScsIFsnc2l0ZV9uYW1lJywgJ3NpdGVfZGVzY3JpcHRpb24nLCAnc2l0ZV9rZXl3b3JkcyddKVxuXG4gIGNvbnN0IHNpdGVOYW1lID0gc2V0dGluZ3M/LmZpbmQocyA9PiBzLmtleSA9PT0gJ3NpdGVfbmFtZScpXG4gIGNvbnN0IHNpdGVEZXNjcmlwdGlvbiA9IHNldHRpbmdzPy5maW5kKHMgPT4gcy5rZXkgPT09ICdzaXRlX2Rlc2NyaXB0aW9uJylcbiAgY29uc3Qgc2l0ZUtleXdvcmRzID0gc2V0dGluZ3M/LmZpbmQocyA9PiBzLmtleSA9PT0gJ3NpdGVfa2V5d29yZHMnKVxuXG4gIGNvbnN0IHRpdGxlID0gbG9jYWxlID09PSAnYXInIFxuICAgID8gc2l0ZU5hbWU/LnZhbHVlX2FyIHx8ICfZhdmG2LXYqSDYp9mE2YPZiNix2LPYp9iqJ1xuICAgIDogc2l0ZU5hbWU/LnZhbHVlX2VuIHx8ICdDb3Vyc2UgUGxhdGZvcm0nXG4gIFxuICBjb25zdCBkZXNjcmlwdGlvbiA9IGxvY2FsZSA9PT0gJ2FyJ1xuICAgID8gc2l0ZURlc2NyaXB0aW9uPy52YWx1ZV9hciB8fCAn2YXZhti12Kkg2KrYudmE2YrZhdmK2Kkg2YXYqtmC2K/ZhdipINmE2KrYudmE2YUg2KfZhNmF2YfYp9ix2KfYqiDYp9mE2KzYr9mK2K/YqSdcbiAgICA6IHNpdGVEZXNjcmlwdGlvbj8udmFsdWVfZW4gfHwgJ0FkdmFuY2VkIGxlYXJuaW5nIHBsYXRmb3JtIGZvciBuZXcgc2tpbGxzJ1xuXG4gIGNvbnN0IGtleXdvcmRzID0gbG9jYWxlID09PSAnYXInXG4gICAgPyBzaXRlS2V5d29yZHM/LnZhbHVlX2FyIHx8ICfZg9mI2LHYs9in2KosINiq2LnZhNmK2YUsINmF2YfYp9ix2KfYqidcbiAgICA6IHNpdGVLZXl3b3Jkcz8udmFsdWVfZW4gfHwgJ2NvdXJzZXMsIGVkdWNhdGlvbiwgc2tpbGxzJ1xuXG4gIHJldHVybiB7XG4gICAgdGl0bGUsXG4gICAgZGVzY3JpcHRpb24sXG4gICAga2V5d29yZHMsXG4gICAgb3BlbkdyYXBoOiB7XG4gICAgICB0aXRsZSxcbiAgICAgIGRlc2NyaXB0aW9uLFxuICAgICAgdHlwZTogJ3dlYnNpdGUnLFxuICAgIH0sXG4gICAgYWx0ZXJuYXRlczoge1xuICAgICAgbGFuZ3VhZ2VzOiB7XG4gICAgICAgICdhcic6IGAvYXJgLFxuICAgICAgICAnZW4nOiBgL2VuYCxcbiAgICAgIH0sXG4gICAgfSxcbiAgfVxufVxuXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBIb21lUGFnZSh7IHBhcmFtcyB9OiBIb21lUGFnZVByb3BzKSB7XG4gIGNvbnN0IHsgbG9jYWxlIH0gPSBwYXJhbXNcbiAgY29uc3Qgc3VwYWJhc2UgPSBhd2FpdCBjcmVhdGVDbGllbnQoKVxuXG4gIC8vIEZldGNoIGZlYXR1cmVkIGNvdXJzZXNcbiAgY29uc3QgeyBkYXRhOiBjb3Vyc2VzIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgIC5mcm9tKCdjb3Vyc2VzJylcbiAgICAuc2VsZWN0KGBcbiAgICAgICosXG4gICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopLFxuICAgICAgdmlkZW9zOmNvdXJzZV92aWRlb3MoKilcbiAgICBgKVxuICAgIC5saW1pdCg2KVxuICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuXG4gIC8vIEZldGNoIGNhdGVnb3JpZXNcbiAgY29uc3QgeyBkYXRhOiBjYXRlZ29yaWVzIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgIC5mcm9tKCdjYXRlZ29yaWVzJylcbiAgICAuc2VsZWN0KCcqJylcbiAgICAubGltaXQoNilcblxuICBjb25zdCBzdGF0cyA9IFtcbiAgICB7XG4gICAgICBpY29uOiBCb29rT3BlbixcbiAgICAgIHZhbHVlOiAnNTAwKycsXG4gICAgICBsYWJlbDogbG9jYWxlID09PSAnYXInID8gJ9mD2YjYsdizINmF2KrYp9itJyA6ICdBdmFpbGFibGUgQ291cnNlcycsXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiBVc2VycyxcbiAgICAgIHZhbHVlOiAnMTBLKycsXG4gICAgICBsYWJlbDogbG9jYWxlID09PSAnYXInID8gJ9i32KfZhNioINmG2LTYtycgOiAnQWN0aXZlIFN0dWRlbnRzJyxcbiAgICB9LFxuICAgIHtcbiAgICAgIGljb246IEF3YXJkLFxuICAgICAgdmFsdWU6ICc5NSUnLFxuICAgICAgbGFiZWw6IGxvY2FsZSA9PT0gJ2FyJyA/ICfZhdi52K/ZhCDYp9mE2LHYttinJyA6ICdTYXRpc2ZhY3Rpb24gUmF0ZScsXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiBUcmVuZGluZ1VwLFxuICAgICAgdmFsdWU6ICcyNC83JyxcbiAgICAgIGxhYmVsOiBsb2NhbGUgPT09ICdhcicgPyAn2K/YudmFINmF2KrZiNin2LXZhCcgOiAnU3VwcG9ydCBBdmFpbGFibGUnLFxuICAgIH0sXG4gIF1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xMlwiPlxuICAgICAgey8qIEhlcm8gU2VjdGlvbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1wdXJwbGUtNzAwIHRleHQtd2hpdGUgcHktMjBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBtZDp0ZXh0LTZ4bCBmb250LWJvbGQgbWItNlwiPlxuICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInIFxuICAgICAgICAgICAgICAgID8gJ9iq2LnZhNmFINmF2YfYp9ix2KfYqiDYrNiv2YrYr9ipINmI2KfZg9iz2Kgg2YbZgtin2LcnXG4gICAgICAgICAgICAgICAgOiAnTGVhcm4gTmV3IFNraWxscyAmIEVhcm4gUG9pbnRzJ1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBtZDp0ZXh0LTJ4bCBtYi04IHRleHQtYmx1ZS0xMDAgbWF4LXctM3hsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2FyJ1xuICAgICAgICAgICAgICAgID8gJ9mF2YbYtdipINiq2LnZhNmK2YXZitipINmF2KrZgtiv2YXYqSDYqtis2YXYuSDYqNmK2YYg2KfZhNiq2LnZhNmFINmI2YPYs9ioINin2YTZhtmC2KfYtyDZhdmGINiu2YTYp9mEINil2YPZhdin2YQg2KfZhNi52LHZiNi2INmI2KfZhNmF2YfYp9mFJ1xuICAgICAgICAgICAgICAgIDogJ0FkdmFuY2VkIGxlYXJuaW5nIHBsYXRmb3JtIHRoYXQgY29tYmluZXMgZWR1Y2F0aW9uIHdpdGggZWFybmluZyBwb2ludHMgdGhyb3VnaCBjb21wbGV0aW5nIG9mZmVycyBhbmQgdGFza3MnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8TGluayBocmVmPXtgLyR7bG9jYWxlfS9jb3Vyc2VzYH0+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBzaXplPVwibGdcIiBjbGFzc05hbWU9XCJiZy13aGl0ZSB0ZXh0LWJsdWUtNjAwIGhvdmVyOmJnLWdyYXktMTAwXCI+XG4gICAgICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInID8gJ9iq2LXZgditINin2YTZg9mI2LHYs9in2KonIDogJ0Jyb3dzZSBDb3Vyc2VzJ31cbiAgICAgICAgICAgICAgICAgIDxBcnJvd1JpZ2h0IGNsYXNzTmFtZT1cIm1sLTIgaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj17YC8ke2xvY2FsZX0vZWFybi1wb2ludHNgfT5cbiAgICAgICAgICAgICAgICA8QnV0dG9uIHNpemU9XCJsZ1wiIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwiYm9yZGVyLXdoaXRlIHRleHQtd2hpdGUgaG92ZXI6Ymctd2hpdGUgaG92ZXI6dGV4dC1ibHVlLTYwMFwiPlxuICAgICAgICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2FyJyA/ICfYp9io2K/YoyDZg9iz2Kgg2KfZhNmG2YLYp9i3JyA6ICdTdGFydCBFYXJuaW5nIFBvaW50cyd9XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIFN0YXRzIFNlY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xMlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy00IGdhcC04XCI+XG4gICAgICAgICAgICB7c3RhdHMubWFwKChzdGF0LCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBJY29uID0gc3RhdC5pY29uXG4gICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdy0xNiBoLTE2IGJnLWJsdWUtMTAwIHJvdW5kZWQtZnVsbCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPntzdGF0LnZhbHVlfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+e3N0YXQubGFiZWx9PC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIClcbiAgICAgICAgICAgIH0pfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIENvbnRlbnQgVG9wIEFkcyAqL31cbiAgICAgIDxBZFNwYWNlIHBvc2l0aW9uPVwiY29udGVudC10b3BcIiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiIC8+XG5cbiAgICAgIHsvKiBGZWF0dXJlZCBDb3Vyc2VzICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMTJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTJcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICAgIHtsb2NhbGUgPT09ICdhcicgPyAn2KfZhNmD2YjYsdiz2KfYqiDYp9mE2YXZhdmK2LLYqScgOiAnRmVhdHVyZWQgQ291cnNlcyd9XG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInXG4gICAgICAgICAgICAgICAgPyAn2KfZg9iq2LTZgSDYo9it2K/YqyDYp9mE2YPZiNix2LPYp9iqINin2YTZhdiq2KfYrdipINmI2KfYqNiv2KMg2LHYrdmE2Kkg2KfZhNiq2LnZhNmFINin2YTZitmI2YUnXG4gICAgICAgICAgICAgICAgOiAnRGlzY292ZXIgdGhlIGxhdGVzdCBhdmFpbGFibGUgY291cnNlcyBhbmQgc3RhcnQgeW91ciBsZWFybmluZyBqb3VybmV5IHRvZGF5J1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Y291cnNlcyAmJiBjb3Vyc2VzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgICAgICAge2NvdXJzZXMubWFwKChjb3Vyc2UpID0+IChcbiAgICAgICAgICAgICAgICA8Q291cnNlQ2FyZFxuICAgICAgICAgICAgICAgICAga2V5PXtjb3Vyc2UuaWR9XG4gICAgICAgICAgICAgICAgICBjb3Vyc2U9e2NvdXJzZSBhcyBDb3Vyc2V9XG4gICAgICAgICAgICAgICAgICBsb2NhbGU9e2xvY2FsZSBhcyBMYW5ndWFnZX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInID8gJ9mE2Kcg2KrZiNis2K8g2YPZiNix2LPYp9iqINmF2KrYp9it2Kkg2K3Yp9mE2YrYp9mLJyA6ICdObyBjb3Vyc2VzIGF2YWlsYWJsZSBhdCB0aGUgbW9tZW50J31cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbXQtMTJcIj5cbiAgICAgICAgICAgIDxMaW5rIGhyZWY9e2AvJHtsb2NhbGV9L2NvdXJzZXNgfT5cbiAgICAgICAgICAgICAgPEJ1dHRvbiBzaXplPVwibGdcIj5cbiAgICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInID8gJ9i52LHYtiDYrNmF2YrYuSDYp9mE2YPZiNix2LPYp9iqJyA6ICdWaWV3IEFsbCBDb3Vyc2VzJ31cbiAgICAgICAgICAgICAgICA8QXJyb3dSaWdodCBjbGFzc05hbWU9XCJtbC0yIGgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBCZXR3ZWVuIENvdXJzZXMgQWRzICovfVxuICAgICAgPEFkU3BhY2UgcG9zaXRpb249XCJiZXR3ZWVuLWNvdXJzZXNcIiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiIC8+XG5cbiAgICAgIHsvKiBDYXRlZ29yaWVzIFNlY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xMiBiZy1ncmF5LTUwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInID8gJ9iq2LXZgditINit2LPYqCDYp9mE2KrYtdmG2YrZgScgOiAnQnJvd3NlIGJ5IENhdGVnb3J5J31cbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1heC13LTJ4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgIHtsb2NhbGUgPT09ICdhcidcbiAgICAgICAgICAgICAgICA/ICfYp9iu2KrYsSDYp9mE2KrYtdmG2YrZgSDYp9mE2LDZiiDZitmG2KfYs9ioINin2YfYqtmF2KfZhdin2KrZgyDZiNin2KjYr9ijINin2YTYqti52YTZhSdcbiAgICAgICAgICAgICAgICA6ICdDaG9vc2UgdGhlIGNhdGVnb3J5IHRoYXQgbWF0Y2hlcyB5b3VyIGludGVyZXN0cyBhbmQgc3RhcnQgbGVhcm5pbmcnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHtjYXRlZ29yaWVzICYmIGNhdGVnb3JpZXMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtMyBsZzpncmlkLWNvbHMtNiBnYXAtNlwiPlxuICAgICAgICAgICAgICB7Y2F0ZWdvcmllcy5tYXAoKGNhdGVnb3J5KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgY2F0ZWdvcnlOYW1lID0gbG9jYWxlID09PSAnYXInID8gY2F0ZWdvcnkubmFtZV9hciA6IGNhdGVnb3J5Lm5hbWVfZW5cbiAgICAgICAgICAgICAgICBjb25zdCBjYXRlZ29yeVNsdWcgPSBsb2NhbGUgPT09ICdhcicgPyBjYXRlZ29yeS5zbHVnX2FyIDogY2F0ZWdvcnkuc2x1Z19lblxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICBrZXk9e2NhdGVnb3J5LmlkfVxuICAgICAgICAgICAgICAgICAgICBocmVmPXtgLyR7bG9jYWxlfS9jYXRlZ29yaWVzLyR7Y2F0ZWdvcnlTbHVnfWB9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBncm91cC1ob3ZlcjpzY2FsZS0xMDVcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAwIHRvLXB1cnBsZS02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQteGwgZm9udC1ib2xkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NhdGVnb3J5TmFtZS5jaGFyQXQoMCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1zbSBncm91cC1ob3Zlcjp0ZXh0LWJsdWUtNjAwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeU5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgIHtsb2NhbGUgPT09ICdhcicgPyAn2YTYpyDYqtmI2KzYryDYqti12YbZitmB2KfYqiDZhdiq2KfYrdipINit2KfZhNmK2KfZiycgOiAnTm8gY2F0ZWdvcmllcyBhdmFpbGFibGUgYXQgdGhlIG1vbWVudCd9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogQ29udGVudCBCb3R0b20gQWRzICovfVxuICAgICAgPEFkU3BhY2UgcG9zaXRpb249XCJjb250ZW50LWJvdHRvbVwiIGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCIgLz5cblxuICAgICAgey8qIENUQSBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMjAgYmctYmx1ZS02MDAgdGV4dC13aGl0ZVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIG1kOnRleHQtNHhsIGZvbnQtYm9sZCBtYi02XCI+XG4gICAgICAgICAgICB7bG9jYWxlID09PSAnYXInIFxuICAgICAgICAgICAgICA/ICfYp9io2K/YoyDYsdit2YTYqSDYp9mE2KrYudmE2YUg2KfZhNmK2YjZhSdcbiAgICAgICAgICAgICAgOiAnU3RhcnQgWW91ciBMZWFybmluZyBKb3VybmV5IFRvZGF5J1xuICAgICAgICAgICAgfVxuICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBtYi04IHRleHQtYmx1ZS0xMDAgbWF4LXctMnhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgIHtsb2NhbGUgPT09ICdhcidcbiAgICAgICAgICAgICAgPyAn2KfZhti22YUg2KXZhNmJINii2YTYp9mBINin2YTYt9mE2KfYqCDZiNin2YPYs9ioINmG2YLYp9i3INij2KvZhtin2KEg2KrYudmE2YUg2YXZh9in2LHYp9iqINis2K/Zitiv2KknXG4gICAgICAgICAgICAgIDogJ0pvaW4gdGhvdXNhbmRzIG9mIHN0dWRlbnRzIGFuZCBlYXJuIHBvaW50cyB3aGlsZSBsZWFybmluZyBuZXcgc2tpbGxzJ1xuICAgICAgICAgICAgfVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8TGluayBocmVmPXtgLyR7bG9jYWxlfS9yZWdpc3RlcmB9PlxuICAgICAgICAgICAgPEJ1dHRvbiBzaXplPVwibGdcIiBjbGFzc05hbWU9XCJiZy13aGl0ZSB0ZXh0LWJsdWUtNjAwIGhvdmVyOmJnLWdyYXktMTAwXCI+XG4gICAgICAgICAgICAgIHtsb2NhbGUgPT09ICdhcicgPyAn2KXZhti02KfYoSDYrdiz2KfYqCDZhdis2KfZhtmKJyA6ICdDcmVhdGUgRnJlZSBBY2NvdW50J31cbiAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwibWwtMiBoLTUgdy01XCIgLz5cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJMaW5rIiwiQm9va09wZW4iLCJVc2VycyIsIkF3YXJkIiwiVHJlbmRpbmdVcCIsIkFycm93UmlnaHQiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDb3Vyc2VDYXJkIiwiQWRTcGFjZSIsImNyZWF0ZUNsaWVudCIsImdlbmVyYXRlTWV0YWRhdGEiLCJwYXJhbXMiLCJsb2NhbGUiLCJzdXBhYmFzZSIsImRhdGEiLCJzZXR0aW5ncyIsImZyb20iLCJzZWxlY3QiLCJpbiIsInNpdGVOYW1lIiwiZmluZCIsInMiLCJrZXkiLCJzaXRlRGVzY3JpcHRpb24iLCJzaXRlS2V5d29yZHMiLCJ0aXRsZSIsInZhbHVlX2FyIiwidmFsdWVfZW4iLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwib3BlbkdyYXBoIiwidHlwZSIsImFsdGVybmF0ZXMiLCJsYW5ndWFnZXMiLCJIb21lUGFnZSIsImNvdXJzZXMiLCJsaW1pdCIsIm9yZGVyIiwiYXNjZW5kaW5nIiwiY2F0ZWdvcmllcyIsInN0YXRzIiwiaWNvbiIsInZhbHVlIiwibGFiZWwiLCJkaXYiLCJjbGFzc05hbWUiLCJzZWN0aW9uIiwiaDEiLCJwIiwiaHJlZiIsInNpemUiLCJ2YXJpYW50IiwibWFwIiwic3RhdCIsImluZGV4IiwiSWNvbiIsInBvc2l0aW9uIiwiaDIiLCJsZW5ndGgiLCJjb3Vyc2UiLCJpZCIsImNhdGVnb3J5IiwiY2F0ZWdvcnlOYW1lIiwibmFtZV9hciIsIm5hbWVfZW4iLCJjYXRlZ29yeVNsdWciLCJzbHVnX2FyIiwic2x1Z19lbiIsInNwYW4iLCJjaGFyQXQiLCJoMyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3edb23e27f35\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbW9hbWVcXERlc2t0b3BcXDMzXFxjb3Vyc2UtcGxhdGZvcm1cXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzZWRiMjNlMjdmMzVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Course Platform - Learn New Skills\",\n    description: \"Advanced learning platform for new skills with points system\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZpQjtBQUloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwySkFBZTtzQkFDN0JLOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1vYW1lXFxEZXNrdG9wXFwzM1xcY291cnNlLXBsYXRmb3JtXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQ291cnNlIFBsYXRmb3JtIC0gTGVhcm4gTmV3IFNraWxsc1wiLFxuICBkZXNjcmlwdGlvbjogXCJBZHZhbmNlZCBsZWFybmluZyBwbGF0Zm9ybSBmb3IgbmV3IHNraWxscyB3aXRoIHBvaW50cyBzeXN0ZW1cIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmoame%5CDesktop%5C33%5Ccourse-platform%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmoame%5CDesktop%5C33%5Ccourse-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmoame%5CDesktop%5C33%5Ccourse-platform%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmoame%5CDesktop%5C33%5Ccourse-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/layout.tsx */ \"(rsc)/./app/[locale]/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/page.tsx */ \"(rsc)/./app/[locale]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/page\",\n        pathname: \"/[locale]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmoame%5CDesktop%5C33%5Ccourse-platform%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmoame%5CDesktop%5C33%5Ccourse-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Capp%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Capp%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/layout.tsx */ \"(rsc)/./app/[locale]/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vYW1lJTVDJTVDRGVza3RvcCU1QyU1QzMzJTVDJTVDY291cnNlLXBsYXRmb3JtJTVDJTVDYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQThHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtb2FtZVxcXFxEZXNrdG9wXFxcXDMzXFxcXGNvdXJzZS1wbGF0Zm9ybVxcXFxhcHBcXFxcW2xvY2FsZV1cXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Capp%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5Cad-space.tsx%22%2C%22ids%22%3A%5B%22AdSpace%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Csrc%5C%5Ccomponents%5C%5Ccourses%5C%5Ccourse-card.tsx%22%2C%22ids%22%3A%5B%22CourseCard%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5Cad-space.tsx%22%2C%22ids%22%3A%5B%22AdSpace%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Csrc%5C%5Ccomponents%5C%5Ccourses%5C%5Ccourse-card.tsx%22%2C%22ids%22%3A%5B%22CourseCard%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ads/ad-space.tsx */ \"(rsc)/./src/components/ads/ad-space.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/courses/course-card.tsx */ \"(rsc)/./src/components/courses/course-card.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vYW1lJTVDJTVDRGVza3RvcCU1QyU1QzMzJTVDJTVDY291cnNlLXBsYXRmb3JtJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vYW1lJTVDJTVDRGVza3RvcCU1QyU1QzMzJTVDJTVDY291cnNlLXBsYXRmb3JtJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2FkcyU1QyU1Q2FkLXNwYWNlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkFkU3BhY2UlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbW9hbWUlNUMlNUNEZXNrdG9wJTVDJTVDMzMlNUMlNUNjb3Vyc2UtcGxhdGZvcm0lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDY291cnNlcyU1QyU1Q2NvdXJzZS1jYXJkLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNvdXJzZUNhcmQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUFpTDtBQUNqTDtBQUNBLDhLQUFvSjtBQUNwSjtBQUNBLDRMQUE4SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vYW1lXFxcXERlc2t0b3BcXFxcMzNcXFxcY291cnNlLXBsYXRmb3JtXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQWRTcGFjZVwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vYW1lXFxcXERlc2t0b3BcXFxcMzNcXFxcY291cnNlLXBsYXRmb3JtXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGFkc1xcXFxhZC1zcGFjZS50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkNvdXJzZUNhcmRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxtb2FtZVxcXFxEZXNrdG9wXFxcXDMzXFxcXGNvdXJzZS1wbGF0Zm9ybVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxjb3Vyc2VzXFxcXGNvdXJzZS1jYXJkLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5Cad-space.tsx%22%2C%22ids%22%3A%5B%22AdSpace%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Csrc%5C%5Ccomponents%5C%5Ccourses%5C%5Ccourse-card.tsx%22%2C%22ids%22%3A%5B%22CourseCard%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtb2FtZVxcRGVza3RvcFxcMzNcXGNvdXJzZS1wbGF0Zm9ybVxcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/components/ads/ad-space.tsx":
/*!*****************************************!*\
  !*** ./src/components/ads/ad-space.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdSpace: () => (/* binding */ AdSpace),
/* harmony export */   GoogleAdSense: () => (/* binding */ GoogleAdSense)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AdSpace = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AdSpace() from the server but AdSpace is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\33\\course-platform\\src\\components\\ads\\ad-space.tsx",
"AdSpace",
);const GoogleAdSense = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call GoogleAdSense() from the server but GoogleAdSense is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\33\\course-platform\\src\\components\\ads\\ad-space.tsx",
"GoogleAdSense",
);

/***/ }),

/***/ "(rsc)/./src/components/courses/course-card.tsx":
/*!************************************************!*\
  !*** ./src/components/courses/course-card.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CourseCard: () => (/* binding */ CourseCard)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const CourseCard = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CourseCard() from the server but CourseCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\33\\course-platform\\src\\components\\courses\\course-card.tsx",
"CourseCard",
);

/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"your_supabase_url_here\", \"your_supabase_anon_key_here\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   extractVideoId: () => (/* binding */ extractVideoId),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPoints: () => (/* binding */ formatPoints),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getVideoEmbedUrl: () => (/* binding */ getVideoEmbedUrl),\n/* harmony export */   isRTL: () => (/* binding */ isRTL),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date, locale = 'ar') {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    if (locale === 'ar') {\n        return new Intl.DateTimeFormat('ar-SA', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        }).format(dateObj);\n    }\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(dateObj);\n}\nfunction formatPoints(points, locale = 'ar') {\n    if (locale === 'ar') {\n        return `${points.toLocaleString('ar-SA')} نقطة`;\n    }\n    return `${points.toLocaleString('en-US')} points`;\n}\nfunction generateSlug(text) {\n    return text.toLowerCase().trim().replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\n    ;\n}\nfunction extractVideoId(url, type) {\n    if (type === 'youtube') {\n        const regex = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n        const match = url.match(regex);\n        return match ? match[1] : null;\n    }\n    if (type === 'vimeo') {\n        const regex = /(?:vimeo\\.com\\/)([0-9]+)/;\n        const match = url.match(regex);\n        return match ? match[1] : null;\n    }\n    return null;\n}\nfunction getVideoEmbedUrl(url, type) {\n    switch(type){\n        case 'youtube':\n            const youtubeId = extractVideoId(url, 'youtube');\n            return youtubeId ? `https://www.youtube.com/embed/${youtubeId}` : url;\n        case 'vimeo':\n            const vimeoId = extractVideoId(url, 'vimeo');\n            return vimeoId ? `https://player.vimeo.com/video/${vimeoId}` : url;\n        case 'drive':\n            // Convert Google Drive share link to embed link\n            const driveRegex = /\\/file\\/d\\/([a-zA-Z0-9-_]+)/;\n            const driveMatch = url.match(driveRegex);\n            return driveMatch ? `https://drive.google.com/file/d/${driveMatch[1]}/preview` : url;\n        case 'direct':\n        default:\n            return url;\n    }\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).trim() + '...';\n}\nfunction isRTL(locale) {\n    return locale === 'ar';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/layout.tsx":
/*!*********************************!*\
  !*** ./app/[locale]/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/header */ \"(ssr)/./src/components/layout/header.tsx\");\n/* harmony import */ var _components_layout_footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/footer */ \"(ssr)/./src/components/layout/footer.tsx\");\n/* harmony import */ var _components_ads_ad_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ads/ad-space */ \"(ssr)/./src/components/ads/ad-space.tsx\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _store_site__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/site */ \"(ssr)/./src/store/site.ts\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction LocaleLayout({ children, params }) {\n    const { locale } = params;\n    const { setUser, setAppUser, setLoading } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_5__.useAuthStore)();\n    const { setLanguage, setSettings, setAdsSettings, setLoading: setSiteLoading } = (0,_store_site__WEBPACK_IMPORTED_MODULE_6__.useSiteStore)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__.createClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LocaleLayout.useEffect\": ()=>{\n            // Set language\n            setLanguage(locale);\n            // Load site settings\n            const loadSiteSettings = {\n                \"LocaleLayout.useEffect.loadSiteSettings\": async ()=>{\n                    try {\n                        const { data: settings } = await supabase.from('site_settings').select('*');\n                        const { data: adsSettings } = await supabase.from('ads_settings').select('*').eq('is_active', true);\n                        if (settings) setSettings(settings);\n                        if (adsSettings) setAdsSettings(adsSettings);\n                    } catch (error) {\n                        console.error('Error loading site settings:', error);\n                    } finally{\n                        setSiteLoading(false);\n                    }\n                }\n            }[\"LocaleLayout.useEffect.loadSiteSettings\"];\n            loadSiteSettings();\n        }\n    }[\"LocaleLayout.useEffect\"], [\n        locale,\n        setLanguage,\n        setSettings,\n        setAdsSettings,\n        setSiteLoading,\n        supabase\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LocaleLayout.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"LocaleLayout.useEffect.getInitialSession\": async ()=>{\n                    try {\n                        const { data: { session } } = await supabase.auth.getSession();\n                        if (session?.user) {\n                            setUser(session.user);\n                            // Get app user data\n                            const { data: appUser } = await supabase.from('users').select('*').eq('id', session.user.id).single();\n                            if (appUser) {\n                                setAppUser(appUser);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error getting initial session:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"LocaleLayout.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"LocaleLayout.useEffect\": async (event, session)=>{\n                    if (event === 'SIGNED_IN' && session?.user) {\n                        setUser(session.user);\n                        // Get or create app user data\n                        const { data: appUser, error } = await supabase.from('users').select('*').eq('id', session.user.id).single();\n                        if (error && error.code === 'PGRST116') {\n                            // User doesn't exist, create one\n                            const { data: newUser } = await supabase.from('users').insert({\n                                id: session.user.id,\n                                email: session.user.email,\n                                username: session.user.email.split('@')[0],\n                                points: 0\n                            }).select().single();\n                            if (newUser) {\n                                setAppUser(newUser);\n                            }\n                        } else if (appUser) {\n                            setAppUser(appUser);\n                        }\n                    } else if (event === 'SIGNED_OUT') {\n                        setUser(null);\n                        setAppUser(null);\n                    }\n                }\n            }[\"LocaleLayout.useEffect\"]);\n            return ({\n                \"LocaleLayout.useEffect\": ()=>subscription.unsubscribe()\n            })[\"LocaleLayout.useEffect\"];\n        }\n    }[\"LocaleLayout.useEffect\"], [\n        setUser,\n        setAppUser,\n        setLoading,\n        supabase\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen flex flex-col ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.isRTL)(locale) ? 'rtl' : 'ltr'}`,\n        dir: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.isRTL)(locale) ? 'rtl' : 'ltr',\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_ad_space__WEBPACK_IMPORTED_MODULE_4__.AdSpace, {\n                position: \"header\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_2__.Header, {\n                locale: locale\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_footer__WEBPACK_IMPORTED_MODULE_3__.Footer, {\n                locale: locale\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_ad_space__WEBPACK_IMPORTED_MODULE_4__.AdSpace, {\n                position: \"footer\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                async: true,\n                src: \"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js\",\n                crossOrigin: \"anonymous\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Capp%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Capp%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/layout.tsx */ \"(ssr)/./app/[locale]/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vYW1lJTVDJTVDRGVza3RvcCU1QyU1QzMzJTVDJTVDY291cnNlLXBsYXRmb3JtJTVDJTVDYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQThHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtb2FtZVxcXFxEZXNrdG9wXFxcXDMzXFxcXGNvdXJzZS1wbGF0Zm9ybVxcXFxhcHBcXFxcW2xvY2FsZV1cXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Capp%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5Cad-space.tsx%22%2C%22ids%22%3A%5B%22AdSpace%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Csrc%5C%5Ccomponents%5C%5Ccourses%5C%5Ccourse-card.tsx%22%2C%22ids%22%3A%5B%22CourseCard%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5Cad-space.tsx%22%2C%22ids%22%3A%5B%22AdSpace%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Csrc%5C%5Ccomponents%5C%5Ccourses%5C%5Ccourse-card.tsx%22%2C%22ids%22%3A%5B%22CourseCard%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ads/ad-space.tsx */ \"(ssr)/./src/components/ads/ad-space.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/courses/course-card.tsx */ \"(ssr)/./src/components/courses/course-card.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vYW1lJTVDJTVDRGVza3RvcCU1QyU1QzMzJTVDJTVDY291cnNlLXBsYXRmb3JtJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vYW1lJTVDJTVDRGVza3RvcCU1QyU1QzMzJTVDJTVDY291cnNlLXBsYXRmb3JtJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2FkcyU1QyU1Q2FkLXNwYWNlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkFkU3BhY2UlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbW9hbWUlNUMlNUNEZXNrdG9wJTVDJTVDMzMlNUMlNUNjb3Vyc2UtcGxhdGZvcm0lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDY291cnNlcyU1QyU1Q2NvdXJzZS1jYXJkLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNvdXJzZUNhcmQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUFpTDtBQUNqTDtBQUNBLDhLQUFvSjtBQUNwSjtBQUNBLDRMQUE4SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vYW1lXFxcXERlc2t0b3BcXFxcMzNcXFxcY291cnNlLXBsYXRmb3JtXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQWRTcGFjZVwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vYW1lXFxcXERlc2t0b3BcXFxcMzNcXFxcY291cnNlLXBsYXRmb3JtXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGFkc1xcXFxhZC1zcGFjZS50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkNvdXJzZUNhcmRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxtb2FtZVxcXFxEZXNrdG9wXFxcXDMzXFxcXGNvdXJzZS1wbGF0Zm9ybVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxjb3Vyc2VzXFxcXGNvdXJzZS1jYXJkLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5Cad-space.tsx%22%2C%22ids%22%3A%5B%22AdSpace%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Csrc%5C%5Ccomponents%5C%5Ccourses%5C%5Ccourse-card.tsx%22%2C%22ids%22%3A%5B%22CourseCard%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoame%5C%5CDesktop%5C%5C33%5C%5Ccourse-platform%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/ads/ad-space.tsx":
/*!*****************************************!*\
  !*** ./src/components/ads/ad-space.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdSpace: () => (/* binding */ AdSpace),\n/* harmony export */   GoogleAdSense: () => (/* binding */ GoogleAdSense)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_site__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/site */ \"(ssr)/./src/store/site.ts\");\n/* __next_internal_client_entry_do_not_use__ AdSpace,GoogleAdSense auto */ \n\n\nfunction AdSpace({ position, className = '' }) {\n    const { getAdsByPosition } = (0,_store_site__WEBPACK_IMPORTED_MODULE_2__.useSiteStore)();\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdSpace.useEffect\": ()=>{\n            const adsForPosition = getAdsByPosition(position);\n            setAds(adsForPosition);\n        }\n    }[\"AdSpace.useEffect\"], [\n        position,\n        getAdsByPosition\n    ]);\n    if (ads.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `ad-space ad-position-${position} ${className}`,\n        children: ads.map((ad)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ad-container\",\n                dangerouslySetInnerHTML: {\n                    __html: ad.ad_code\n                }\n            }, ad.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ads\\\\ad-space.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ads\\\\ad-space.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\nfunction GoogleAdSense({ adSlot, adFormat = 'auto', fullWidthResponsive = true, className = '' }) {\n    const { getSettingValue } = (0,_store_site__WEBPACK_IMPORTED_MODULE_2__.useSiteStore)();\n    const adsenseId = getSettingValue('google_adsense_id');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GoogleAdSense.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"GoogleAdSense.useEffect\"], [\n        adsenseId\n    ]);\n    if (!adsenseId) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `adsense-container ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n            className: \"adsbygoogle\",\n            style: {\n                display: 'block'\n            },\n            \"data-ad-client\": adsenseId,\n            \"data-ad-slot\": adSlot,\n            \"data-ad-format\": adFormat,\n            \"data-full-width-responsive\": fullWidthResponsive.toString()\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ads\\\\ad-space.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ads\\\\ad-space.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ads/ad-space.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/courses/course-card.tsx":
/*!************************************************!*\
  !*** ./src/components/courses/course-card.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CourseCard: () => (/* binding */ CourseCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Gift_Lock_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Gift,Lock,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Gift_Lock_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Gift,Lock,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Gift_Lock_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Gift,Lock,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Gift_Lock_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Gift,Lock,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Gift_Lock_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Gift,Lock,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ CourseCard auto */ \n\n\n\n\n\n\nfunction CourseCard({ course, locale, className = '' }) {\n    const title = locale === 'ar' ? course.title_ar : course.title_en;\n    const description = locale === 'ar' ? course.description_ar : course.description_en;\n    const slug = locale === 'ar' ? course.slug_ar : course.slug_en;\n    const categoryName = course.category ? locale === 'ar' ? course.category.name_ar : course.category.name_en : '';\n    const courseUrl = `/${locale}/courses/${slug}`;\n    const categoryUrl = course.category ? `/${locale}/categories/${locale === 'ar' ? course.category.slug_ar : course.category.slug_en}` : '#';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: `group hover:shadow-lg transition-all duration-300 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden rounded-t-lg\",\n                children: [\n                    course.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: course.image_url,\n                        alt: title,\n                        width: 400,\n                        height: 200,\n                        className: \"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white text-lg font-semibold\",\n                            children: title.charAt(0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-3 left-3\",\n                        children: course.is_free ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                            children: locale === 'ar' ? 'مجاني' : 'Free'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Gift_Lock_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-3 w-3 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatPoints)(course.required_points, locale)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    categoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-3 right-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: categoryUrl,\n                            className: \"bg-black bg-opacity-70 text-white px-2 py-1 rounded-full text-xs hover:bg-opacity-90 transition-colors\",\n                            children: categoryName\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-lg line-clamp-2 group-hover:text-blue-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: courseUrl,\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-sm line-clamp-3\",\n                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.truncateText)(description, 120)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-xs text-gray-500 pt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    course.videos && course.videos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Gift_Lock_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    course.videos.length,\n                                                    \" \",\n                                                    locale === 'ar' ? 'فيديو' : 'videos'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Gift_Lock_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    Math.floor(Math.random() * 1000) + 100,\n                                                    \" \",\n                                                    locale === 'ar' ? 'طالب' : 'students'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Gift_Lock_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1 fill-yellow-400 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"4.\",\n                                                    Math.floor(Math.random() * 9) + 1\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardFooter, {\n                className: \"p-4 pt-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: courseUrl,\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        className: \"w-full\",\n                        variant: course.is_free ? \"default\" : \"outline\",\n                        children: course.is_free ? locale === 'ar' ? 'ابدأ التعلم' : 'Start Learning' : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Gift_Lock_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 17\n                                }, this),\n                                locale === 'ar' ? 'يتطلب نقاط' : 'Requires Points'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\courses\\\\course-card.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/courses/course-card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Facebook_Instagram_Mail_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Facebook,Instagram,Mail,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Facebook_Instagram_Mail_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Facebook,Instagram,Mail,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Facebook_Instagram_Mail_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Facebook,Instagram,Mail,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Facebook_Instagram_Mail_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Facebook,Instagram,Mail,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Facebook_Instagram_Mail_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Facebook,Instagram,Mail,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Facebook_Instagram_Mail_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Facebook,Instagram,Mail,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _store_site__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/site */ \"(ssr)/./src/store/site.ts\");\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\n\n\nfunction Footer({ locale }) {\n    const { getSettingValue } = (0,_store_site__WEBPACK_IMPORTED_MODULE_2__.useSiteStore)();\n    const siteName = getSettingValue('site_name', locale);\n    const siteDescription = getSettingValue('site_description', locale);\n    const contactEmail = getSettingValue('contact_email');\n    const facebookUrl = getSettingValue('social_facebook');\n    const twitterUrl = getSettingValue('social_twitter');\n    const instagramUrl = getSettingValue('social_instagram');\n    const youtubeUrl = getSettingValue('social_youtube');\n    const quickLinks = [\n        {\n            name: locale === 'ar' ? 'الرئيسية' : 'Home',\n            href: `/${locale}`\n        },\n        {\n            name: locale === 'ar' ? 'الكورسات' : 'Courses',\n            href: `/${locale}/courses`\n        },\n        {\n            name: locale === 'ar' ? 'التصنيفات' : 'Categories',\n            href: `/${locale}/categories`\n        },\n        {\n            name: locale === 'ar' ? 'جمع النقاط' : 'Earn Points',\n            href: `/${locale}/earn-points`\n        }\n    ];\n    const supportLinks = [\n        {\n            name: locale === 'ar' ? 'اتصل بنا' : 'Contact Us',\n            href: `/${locale}/contact`\n        },\n        {\n            name: locale === 'ar' ? 'الأسئلة الشائعة' : 'FAQ',\n            href: `/${locale}/faq`\n        },\n        {\n            name: locale === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy',\n            href: `/${locale}/privacy`\n        },\n        {\n            name: locale === 'ar' ? 'شروط الاستخدام' : 'Terms of Service',\n            href: `/${locale}/terms`\n        }\n    ];\n    const socialLinks = [\n        {\n            name: 'Facebook',\n            icon: _barrel_optimize_names_BookOpen_Facebook_Instagram_Mail_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            url: facebookUrl\n        },\n        {\n            name: 'Twitter',\n            icon: _barrel_optimize_names_BookOpen_Facebook_Instagram_Mail_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            url: twitterUrl\n        },\n        {\n            name: 'Instagram',\n            icon: _barrel_optimize_names_BookOpen_Facebook_Instagram_Mail_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            url: instagramUrl\n        },\n        {\n            name: 'YouTube',\n            icon: _barrel_optimize_names_BookOpen_Facebook_Instagram_Mail_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            url: youtubeUrl\n        }\n    ].filter((link)=>link.url);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Facebook_Instagram_Mail_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-xl font-bold\",\n                                            children: siteName || 'Course Platform'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-4 leading-relaxed\",\n                                    children: siteDescription || (locale === 'ar' ? 'منصة تعليمية متقدمة لتعلم المهارات الجديدة' : 'Advanced learning platform for new skills')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                contactEmail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Facebook_Instagram_Mail_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: `mailto:${contactEmail}`,\n                                            className: \"hover:text-blue-400 transition-colors\",\n                                            children: contactEmail\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: locale === 'ar' ? 'روابط سريعة' : 'Quick Links'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-blue-400 transition-colors\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: locale === 'ar' ? 'الدعم' : 'Support'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: supportLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-blue-400 transition-colors\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: locale === 'ar' ? 'تابعنا' : 'Follow Us'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: socialLinks.map((social)=>{\n                                        const Icon = social.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: social.url,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-gray-300 hover:text-blue-400 transition-colors\",\n                                            \"aria-label\": social.name,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, social.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    new Date().getFullYear(),\n                                    \" \",\n                                    siteName || 'Course Platform',\n                                    \". \",\n                                    ' ',\n                                    locale === 'ar' ? 'جميع الحقوق محفوظة.' : 'All rights reserved.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: pathname.replace(`/${locale}`, '/ar'),\n                                        className: `text-sm ${locale === 'ar' ? 'text-blue-400' : 'text-gray-400 hover:text-blue-400'} transition-colors`,\n                                        children: \"العربية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"|\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: pathname.replace(`/${locale}`, '/en'),\n                                        className: `text-sm ${locale === 'en' ? 'text-blue-400' : 'text-gray-400 hover:text-blue-400'} transition-colors`,\n                                        children: \"English\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvZm9vdGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRTRCO0FBQ3dEO0FBQ3pDO0FBTXBDLFNBQVNRLE9BQU8sRUFBRUMsTUFBTSxFQUFlO0lBQzVDLE1BQU0sRUFBRUMsZUFBZSxFQUFFLEdBQUdILHlEQUFZQTtJQUV4QyxNQUFNSSxXQUFXRCxnQkFBZ0IsYUFBYUQ7SUFDOUMsTUFBTUcsa0JBQWtCRixnQkFBZ0Isb0JBQW9CRDtJQUM1RCxNQUFNSSxlQUFlSCxnQkFBZ0I7SUFDckMsTUFBTUksY0FBY0osZ0JBQWdCO0lBQ3BDLE1BQU1LLGFBQWFMLGdCQUFnQjtJQUNuQyxNQUFNTSxlQUFlTixnQkFBZ0I7SUFDckMsTUFBTU8sYUFBYVAsZ0JBQWdCO0lBRW5DLE1BQU1RLGFBQWE7UUFDakI7WUFDRUMsTUFBTVYsV0FBVyxPQUFPLGFBQWE7WUFDckNXLE1BQU0sQ0FBQyxDQUFDLEVBQUVYLFFBQVE7UUFDcEI7UUFDQTtZQUNFVSxNQUFNVixXQUFXLE9BQU8sYUFBYTtZQUNyQ1csTUFBTSxDQUFDLENBQUMsRUFBRVgsT0FBTyxRQUFRLENBQUM7UUFDNUI7UUFDQTtZQUNFVSxNQUFNVixXQUFXLE9BQU8sY0FBYztZQUN0Q1csTUFBTSxDQUFDLENBQUMsRUFBRVgsT0FBTyxXQUFXLENBQUM7UUFDL0I7UUFDQTtZQUNFVSxNQUFNVixXQUFXLE9BQU8sZUFBZTtZQUN2Q1csTUFBTSxDQUFDLENBQUMsRUFBRVgsT0FBTyxZQUFZLENBQUM7UUFDaEM7S0FDRDtJQUVELE1BQU1ZLGVBQWU7UUFDbkI7WUFDRUYsTUFBTVYsV0FBVyxPQUFPLGFBQWE7WUFDckNXLE1BQU0sQ0FBQyxDQUFDLEVBQUVYLE9BQU8sUUFBUSxDQUFDO1FBQzVCO1FBQ0E7WUFDRVUsTUFBTVYsV0FBVyxPQUFPLG9CQUFvQjtZQUM1Q1csTUFBTSxDQUFDLENBQUMsRUFBRVgsT0FBTyxJQUFJLENBQUM7UUFDeEI7UUFDQTtZQUNFVSxNQUFNVixXQUFXLE9BQU8sbUJBQW1CO1lBQzNDVyxNQUFNLENBQUMsQ0FBQyxFQUFFWCxPQUFPLFFBQVEsQ0FBQztRQUM1QjtRQUNBO1lBQ0VVLE1BQU1WLFdBQVcsT0FBTyxtQkFBbUI7WUFDM0NXLE1BQU0sQ0FBQyxDQUFDLEVBQUVYLE9BQU8sTUFBTSxDQUFDO1FBQzFCO0tBQ0Q7SUFFRCxNQUFNYSxjQUFjO1FBQ2xCO1lBQUVILE1BQU07WUFBWUksTUFBTXJCLDRIQUFRQTtZQUFFc0IsS0FBS1Y7UUFBWTtRQUNyRDtZQUFFSyxNQUFNO1lBQVdJLE1BQU1wQiw0SEFBT0E7WUFBRXFCLEtBQUtUO1FBQVc7UUFDbEQ7WUFBRUksTUFBTTtZQUFhSSxNQUFNbkIsNEhBQVNBO1lBQUVvQixLQUFLUjtRQUFhO1FBQ3hEO1lBQUVHLE1BQU07WUFBV0ksTUFBTWxCLDRIQUFPQTtZQUFFbUIsS0FBS1A7UUFBVztLQUNuRCxDQUFDUSxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtGLEdBQUc7SUFFekIscUJBQ0UsOERBQUNHO1FBQU9DLFdBQVU7a0JBQ2hCLDRFQUFDQztZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQUlELFdBQVU7O3NDQUViLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQzNCLDRIQUFRQTs0Q0FBQzJCLFdBQVU7Ozs7OztzREFDcEIsOERBQUNFOzRDQUFLRixXQUFVO3NEQUNiakIsWUFBWTs7Ozs7Ozs7Ozs7OzhDQUdqQiw4REFBQ29CO29DQUFFSCxXQUFVOzhDQUNWaEIsbUJBQW9CSCxDQUFBQSxXQUFXLE9BQzVCLCtDQUNBLDJDQUEwQzs7Ozs7O2dDQUcvQ0ksOEJBQ0MsOERBQUNnQjtvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUN0Qiw0SEFBSUE7NENBQUNzQixXQUFVOzs7Ozs7c0RBQ2hCLDhEQUFDSTs0Q0FBRVosTUFBTSxDQUFDLE9BQU8sRUFBRVAsY0FBYzs0Q0FBRWUsV0FBVTtzREFDMUNmOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT1QsOERBQUNnQjs7OENBQ0MsOERBQUNJO29DQUFHTCxXQUFVOzhDQUNYbkIsV0FBVyxPQUFPLGdCQUFnQjs7Ozs7OzhDQUVyQyw4REFBQ3lCO29DQUFHTixXQUFVOzhDQUNYVixXQUFXaUIsR0FBRyxDQUFDLENBQUNULHFCQUNmLDhEQUFDVTtzREFDQyw0RUFBQ3BDLGtEQUFJQTtnREFDSG9CLE1BQU1NLEtBQUtOLElBQUk7Z0RBQ2ZRLFdBQVU7MERBRVRGLEtBQUtQLElBQUk7Ozs7OzsyQ0FMTE8sS0FBS1AsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FheEIsOERBQUNVOzs4Q0FDQyw4REFBQ0k7b0NBQUdMLFdBQVU7OENBQ1huQixXQUFXLE9BQU8sVUFBVTs7Ozs7OzhDQUUvQiw4REFBQ3lCO29DQUFHTixXQUFVOzhDQUNYUCxhQUFhYyxHQUFHLENBQUMsQ0FBQ1QscUJBQ2pCLDhEQUFDVTtzREFDQyw0RUFBQ3BDLGtEQUFJQTtnREFDSG9CLE1BQU1NLEtBQUtOLElBQUk7Z0RBQ2ZRLFdBQVU7MERBRVRGLEtBQUtQLElBQUk7Ozs7OzsyQ0FMTE8sS0FBS1AsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FheEIsOERBQUNVOzs4Q0FDQyw4REFBQ0k7b0NBQUdMLFdBQVU7OENBQ1huQixXQUFXLE9BQU8sV0FBVzs7Ozs7OzhDQUVoQyw4REFBQ29CO29DQUFJRCxXQUFVOzhDQUNaTixZQUFZYSxHQUFHLENBQUMsQ0FBQ0U7d0NBQ2hCLE1BQU1DLE9BQU9ELE9BQU9kLElBQUk7d0NBQ3hCLHFCQUNFLDhEQUFDUzs0Q0FFQ1osTUFBTWlCLE9BQU9iLEdBQUc7NENBQ2hCZSxRQUFPOzRDQUNQQyxLQUFJOzRDQUNKWixXQUFVOzRDQUNWYSxjQUFZSixPQUFPbEIsSUFBSTtzREFFdkIsNEVBQUNtQjtnREFBS1YsV0FBVTs7Ozs7OzJDQVBYUyxPQUFPbEIsSUFBSTs7Ozs7b0NBVXRCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTU4sOERBQUNVO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNHO2dDQUFFSCxXQUFVOztvQ0FBd0I7b0NBQ2hDLElBQUljLE9BQU9DLFdBQVc7b0NBQUc7b0NBQUVoQyxZQUFZO29DQUFrQjtvQ0FBRztvQ0FDOURGLFdBQVcsT0FBTyx3QkFBd0I7Ozs7Ozs7MENBRTdDLDhEQUFDb0I7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDNUIsa0RBQUlBO3dDQUNIb0IsTUFBTXdCLFNBQVNDLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRXBDLFFBQVEsRUFBRTt3Q0FDckNtQixXQUFXLENBQUMsUUFBUSxFQUNsQm5CLFdBQVcsT0FBTyxrQkFBa0Isb0NBQ3JDLGtCQUFrQixDQUFDO2tEQUNyQjs7Ozs7O2tEQUdELDhEQUFDcUI7d0NBQUtGLFdBQVU7a0RBQWdCOzs7Ozs7a0RBQ2hDLDhEQUFDNUIsa0RBQUlBO3dDQUNIb0IsTUFBTXdCLFNBQVNDLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRXBDLFFBQVEsRUFBRTt3Q0FDckNtQixXQUFXLENBQUMsUUFBUSxFQUNsQm5CLFdBQVcsT0FBTyxrQkFBa0Isb0NBQ3JDLGtCQUFrQixDQUFDO2tEQUNyQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNmIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1vYW1lXFxEZXNrdG9wXFwzM1xcY291cnNlLXBsYXRmb3JtXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcZm9vdGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHsgQm9va09wZW4sIEZhY2Vib29rLCBUd2l0dGVyLCBJbnN0YWdyYW0sIFlvdXR1YmUsIE1haWwgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyB1c2VTaXRlU3RvcmUgfSBmcm9tICdAL3N0b3JlL3NpdGUnXG5cbmludGVyZmFjZSBGb290ZXJQcm9wcyB7XG4gIGxvY2FsZTogc3RyaW5nXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBGb290ZXIoeyBsb2NhbGUgfTogRm9vdGVyUHJvcHMpIHtcbiAgY29uc3QgeyBnZXRTZXR0aW5nVmFsdWUgfSA9IHVzZVNpdGVTdG9yZSgpXG4gIFxuICBjb25zdCBzaXRlTmFtZSA9IGdldFNldHRpbmdWYWx1ZSgnc2l0ZV9uYW1lJywgbG9jYWxlIGFzICdhcicgfCAnZW4nKVxuICBjb25zdCBzaXRlRGVzY3JpcHRpb24gPSBnZXRTZXR0aW5nVmFsdWUoJ3NpdGVfZGVzY3JpcHRpb24nLCBsb2NhbGUgYXMgJ2FyJyB8ICdlbicpXG4gIGNvbnN0IGNvbnRhY3RFbWFpbCA9IGdldFNldHRpbmdWYWx1ZSgnY29udGFjdF9lbWFpbCcpXG4gIGNvbnN0IGZhY2Vib29rVXJsID0gZ2V0U2V0dGluZ1ZhbHVlKCdzb2NpYWxfZmFjZWJvb2snKVxuICBjb25zdCB0d2l0dGVyVXJsID0gZ2V0U2V0dGluZ1ZhbHVlKCdzb2NpYWxfdHdpdHRlcicpXG4gIGNvbnN0IGluc3RhZ3JhbVVybCA9IGdldFNldHRpbmdWYWx1ZSgnc29jaWFsX2luc3RhZ3JhbScpXG4gIGNvbnN0IHlvdXR1YmVVcmwgPSBnZXRTZXR0aW5nVmFsdWUoJ3NvY2lhbF95b3V0dWJlJylcblxuICBjb25zdCBxdWlja0xpbmtzID0gW1xuICAgIHtcbiAgICAgIG5hbWU6IGxvY2FsZSA9PT0gJ2FyJyA/ICfYp9mE2LHYptmK2LPZitipJyA6ICdIb21lJyxcbiAgICAgIGhyZWY6IGAvJHtsb2NhbGV9YCxcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6IGxvY2FsZSA9PT0gJ2FyJyA/ICfYp9mE2YPZiNix2LPYp9iqJyA6ICdDb3Vyc2VzJyxcbiAgICAgIGhyZWY6IGAvJHtsb2NhbGV9L2NvdXJzZXNgLFxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogbG9jYWxlID09PSAnYXInID8gJ9in2YTYqti12YbZitmB2KfYqicgOiAnQ2F0ZWdvcmllcycsXG4gICAgICBocmVmOiBgLyR7bG9jYWxlfS9jYXRlZ29yaWVzYCxcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6IGxvY2FsZSA9PT0gJ2FyJyA/ICfYrNmF2Lkg2KfZhNmG2YLYp9i3JyA6ICdFYXJuIFBvaW50cycsXG4gICAgICBocmVmOiBgLyR7bG9jYWxlfS9lYXJuLXBvaW50c2AsXG4gICAgfSxcbiAgXVxuXG4gIGNvbnN0IHN1cHBvcnRMaW5rcyA9IFtcbiAgICB7XG4gICAgICBuYW1lOiBsb2NhbGUgPT09ICdhcicgPyAn2KfYqti12YQg2KjZhtinJyA6ICdDb250YWN0IFVzJyxcbiAgICAgIGhyZWY6IGAvJHtsb2NhbGV9L2NvbnRhY3RgLFxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogbG9jYWxlID09PSAnYXInID8gJ9in2YTYo9iz2KbZhNipINin2YTYtNin2KbYudipJyA6ICdGQVEnLFxuICAgICAgaHJlZjogYC8ke2xvY2FsZX0vZmFxYCxcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6IGxvY2FsZSA9PT0gJ2FyJyA/ICfYs9mK2KfYs9ipINin2YTYrti12YjYtdmK2KknIDogJ1ByaXZhY3kgUG9saWN5JyxcbiAgICAgIGhyZWY6IGAvJHtsb2NhbGV9L3ByaXZhY3lgLFxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogbG9jYWxlID09PSAnYXInID8gJ9i02LHZiNi3INin2YTYp9iz2KrYrtiv2KfZhScgOiAnVGVybXMgb2YgU2VydmljZScsXG4gICAgICBocmVmOiBgLyR7bG9jYWxlfS90ZXJtc2AsXG4gICAgfSxcbiAgXVxuXG4gIGNvbnN0IHNvY2lhbExpbmtzID0gW1xuICAgIHsgbmFtZTogJ0ZhY2Vib29rJywgaWNvbjogRmFjZWJvb2ssIHVybDogZmFjZWJvb2tVcmwgfSxcbiAgICB7IG5hbWU6ICdUd2l0dGVyJywgaWNvbjogVHdpdHRlciwgdXJsOiB0d2l0dGVyVXJsIH0sXG4gICAgeyBuYW1lOiAnSW5zdGFncmFtJywgaWNvbjogSW5zdGFncmFtLCB1cmw6IGluc3RhZ3JhbVVybCB9LFxuICAgIHsgbmFtZTogJ1lvdVR1YmUnLCBpY29uOiBZb3V0dWJlLCB1cmw6IHlvdXR1YmVVcmwgfSxcbiAgXS5maWx0ZXIobGluayA9PiBsaW5rLnVybClcblxuICByZXR1cm4gKFxuICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAgdGV4dC13aGl0ZVwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCBweS0xMlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLThcIj5cbiAgICAgICAgICB7LyogQnJhbmQgU2VjdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTFcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICA8Qm9va09wZW4gY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWJsdWUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiB0ZXh0LXhsIGZvbnQtYm9sZFwiPlxuICAgICAgICAgICAgICAgIHtzaXRlTmFtZSB8fCAnQ291cnNlIFBsYXRmb3JtJ31cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIG1iLTQgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgIHtzaXRlRGVzY3JpcHRpb24gfHwgKGxvY2FsZSA9PT0gJ2FyJyBcbiAgICAgICAgICAgICAgICA/ICfZhdmG2LXYqSDYqti52YTZitmF2YrYqSDZhdiq2YLYr9mF2Kkg2YTYqti52YTZhSDYp9mE2YXZh9in2LHYp9iqINin2YTYrNiv2YrYr9ipJ1xuICAgICAgICAgICAgICAgIDogJ0FkdmFuY2VkIGxlYXJuaW5nIHBsYXRmb3JtIGZvciBuZXcgc2tpbGxzJ1xuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAge2NvbnRhY3RFbWFpbCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgIDxNYWlsIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgPGEgaHJlZj17YG1haWx0bzoke2NvbnRhY3RFbWFpbH1gfSBjbGFzc05hbWU9XCJob3Zlcjp0ZXh0LWJsdWUtNDAwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICB7Y29udGFjdEVtYWlsfVxuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFF1aWNrIExpbmtzICovfVxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTRcIj5cbiAgICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2FyJyA/ICfYsdmI2KfYqNi3INiz2LHZiti52KknIDogJ1F1aWNrIExpbmtzJ31cbiAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIHtxdWlja0xpbmtzLm1hcCgobGluaykgPT4gKFxuICAgICAgICAgICAgICAgIDxsaSBrZXk9e2xpbmsubmFtZX0+XG4gICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICBocmVmPXtsaW5rLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC1ibHVlLTQwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtsaW5rLm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFN1cHBvcnQgTGlua3MgKi99XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPlxuICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInID8gJ9in2YTYr9i52YUnIDogJ1N1cHBvcnQnfVxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAge3N1cHBvcnRMaW5rcy5tYXAoKGxpbmspID0+IChcbiAgICAgICAgICAgICAgICA8bGkga2V5PXtsaW5rLm5hbWV9PlxuICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgaHJlZj17bGluay5ocmVmfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtYmx1ZS00MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7bGluay5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBTb2NpYWwgTWVkaWEgKi99XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPlxuICAgICAgICAgICAgICB7bG9jYWxlID09PSAnYXInID8gJ9iq2KfYqNi52YbYpycgOiAnRm9sbG93IFVzJ31cbiAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIHtzb2NpYWxMaW5rcy5tYXAoKHNvY2lhbCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IEljb24gPSBzb2NpYWwuaWNvblxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICBrZXk9e3NvY2lhbC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICBocmVmPXtzb2NpYWwudXJsfVxuICAgICAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgICAgICByZWw9XCJub29wZW5lciBub3JlZmVycmVyXCJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LWJsdWUtNDAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD17c29jaWFsLm5hbWV9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEJvdHRvbSBTZWN0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTgwMCBtdC04IHB0LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWQ6ZmxleC1yb3cganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgIMKpIHtuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCl9IHtzaXRlTmFtZSB8fCAnQ291cnNlIFBsYXRmb3JtJ30uIHsnICd9XG4gICAgICAgICAgICAgIHtsb2NhbGUgPT09ICdhcicgPyAn2KzZhdmK2Lkg2KfZhNit2YLZiNmCINmF2K3ZgdmI2LjYqS4nIDogJ0FsbCByaWdodHMgcmVzZXJ2ZWQuJ31cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IG10LTQgbWQ6bXQtMFwiPlxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGhyZWY9e3BhdGhuYW1lLnJlcGxhY2UoYC8ke2xvY2FsZX1gLCAnL2FyJyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgICAgbG9jYWxlID09PSAnYXInID8gJ3RleHQtYmx1ZS00MDAnIDogJ3RleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ibHVlLTQwMCdcbiAgICAgICAgICAgICAgICB9IHRyYW5zaXRpb24tY29sb3JzYH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgINin2YTYudix2KjZitipXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPnw8L3NwYW4+XG4gICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgaHJlZj17cGF0aG5hbWUucmVwbGFjZShgLyR7bG9jYWxlfWAsICcvZW4nKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICBsb2NhbGUgPT09ICdlbicgPyAndGV4dC1ibHVlLTQwMCcgOiAndGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWJsdWUtNDAwJ1xuICAgICAgICAgICAgICAgIH0gdHJhbnNpdGlvbi1jb2xvcnNgfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgRW5nbGlzaFxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Zvb3Rlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkxpbmsiLCJCb29rT3BlbiIsIkZhY2Vib29rIiwiVHdpdHRlciIsIkluc3RhZ3JhbSIsIllvdXR1YmUiLCJNYWlsIiwidXNlU2l0ZVN0b3JlIiwiRm9vdGVyIiwibG9jYWxlIiwiZ2V0U2V0dGluZ1ZhbHVlIiwic2l0ZU5hbWUiLCJzaXRlRGVzY3JpcHRpb24iLCJjb250YWN0RW1haWwiLCJmYWNlYm9va1VybCIsInR3aXR0ZXJVcmwiLCJpbnN0YWdyYW1VcmwiLCJ5b3V0dWJlVXJsIiwicXVpY2tMaW5rcyIsIm5hbWUiLCJocmVmIiwic3VwcG9ydExpbmtzIiwic29jaWFsTGlua3MiLCJpY29uIiwidXJsIiwiZmlsdGVyIiwibGluayIsImZvb3RlciIsImNsYXNzTmFtZSIsImRpdiIsInNwYW4iLCJwIiwiYSIsImgzIiwidWwiLCJtYXAiLCJsaSIsInNvY2lhbCIsIkljb24iLCJ0YXJnZXQiLCJyZWwiLCJhcmlhLWxhYmVsIiwiRGF0ZSIsImdldEZ1bGxZZWFyIiwicGF0aG5hbWUiLCJyZXBsYWNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Gift,LogOut,Menu,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Gift,LogOut,Menu,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Gift,LogOut,Menu,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Gift,LogOut,Menu,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Gift,LogOut,Menu,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Gift,LogOut,Menu,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Gift,LogOut,Menu,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _store_site__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/site */ \"(ssr)/./src/store/site.ts\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\n\n\nfunction Header({ locale }) {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { user, appUser, logout } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_5__.useAuthStore)();\n    const { getSettingValue } = (0,_store_site__WEBPACK_IMPORTED_MODULE_6__.useSiteStore)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__.createClient)();\n    const isRTL = locale === 'ar';\n    const siteName = getSettingValue('site_name', locale);\n    const navigation = [\n        {\n            name: locale === 'ar' ? 'الرئيسية' : 'Home',\n            href: `/${locale}`\n        },\n        {\n            name: locale === 'ar' ? 'الكورسات' : 'Courses',\n            href: `/${locale}/courses`\n        },\n        {\n            name: locale === 'ar' ? 'التصنيفات' : 'Categories',\n            href: `/${locale}/categories`\n        },\n        {\n            name: locale === 'ar' ? 'جمع النقاط' : 'Earn Points',\n            href: `/${locale}/earn-points`\n        }\n    ];\n    const handleLogout = async ()=>{\n        await supabase.auth.signOut();\n        logout();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: `/${locale}`,\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-xl font-bold text-gray-900\",\n                                        children: siteName || 'Course Platform'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `px-3 py-2 rounded-md text-sm font-medium transition-colors ${pathname === item.href ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'}`,\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 text-sm text-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: locale === 'ar' ? `${appUser?.points || 0} نقطة` : `${appUser?.points || 0} points`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: appUser?.username\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: `/${locale}/profile`,\n                                                            className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                                    lineNumber: 104,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                locale === 'ar' ? 'الملف الشخصي' : 'Profile'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleLogout,\n                                                            className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                                    lineNumber: 111,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                locale === 'ar' ? 'تسجيل الخروج' : 'Logout'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: `/${locale}/login`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                children: locale === 'ar' ? 'تسجيل الدخول' : 'Login'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: `/${locale}/register`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"sm\",\n                                                children: locale === 'ar' ? 'إنشاء حساب' : 'Register'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: pathname.replace(`/${locale}`, '/ar'),\n                                            className: `px-2 py-1 text-xs rounded ${locale === 'ar' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`,\n                                            children: \"العربية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: pathname.replace(`/${locale}`, '/en'),\n                                            className: `px-2 py-1 text-xs rounded ${locale === 'en' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`,\n                                            children: \"English\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 57\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `block px-3 py-2 rounded-md text-base font-medium ${pathname === item.href ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'}`,\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this)),\n                            user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-4 mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center px-3 py-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Gift_LogOut_Menu_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-700\",\n                                                children: locale === 'ar' ? `${appUser?.points || 0} نقطة` : `${appUser?.points || 0} points`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: `/${locale}/profile`,\n                                        className: \"block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: locale === 'ar' ? 'الملف الشخصي' : 'Profile'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            handleLogout();\n                                            setIsMenuOpen(false);\n                                        },\n                                        className: \"block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50\",\n                                        children: locale === 'ar' ? 'تسجيل الخروج' : 'Logout'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-4 mt-4 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: `/${locale}/login`,\n                                        className: \"block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: locale === 'ar' ? 'تسجيل الدخول' : 'Login'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: `/${locale}/register`,\n                                        className: \"block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: locale === 'ar' ? 'إنشاء حساب' : 'Register'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\layout\\\\header.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\33\\\\course-platform\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"your_supabase_url_here\", \"your_supabase_anon_key_here\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRDtBQUU1QyxTQUFTQztJQUNkLE9BQU9ELGtFQUFtQkEsQ0FDeEJFLHdCQUFvQyxFQUNwQ0EsNkJBQXlDO0FBRTdDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1vYW1lXFxEZXNrdG9wXFwzM1xcY291cnNlLXBsYXRmb3JtXFxzcmNcXGxpYlxcc3VwYWJhc2VcXGNsaWVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVCcm93c2VyQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3NzcidcblxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUNsaWVudCgpIHtcbiAgcmV0dXJuIGNyZWF0ZUJyb3dzZXJDbGllbnQoXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSFcbiAgKVxufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUJyb3dzZXJDbGllbnQiLCJjcmVhdGVDbGllbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   extractVideoId: () => (/* binding */ extractVideoId),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPoints: () => (/* binding */ formatPoints),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getVideoEmbedUrl: () => (/* binding */ getVideoEmbedUrl),\n/* harmony export */   isRTL: () => (/* binding */ isRTL),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date, locale = 'ar') {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    if (locale === 'ar') {\n        return new Intl.DateTimeFormat('ar-SA', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        }).format(dateObj);\n    }\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(dateObj);\n}\nfunction formatPoints(points, locale = 'ar') {\n    if (locale === 'ar') {\n        return `${points.toLocaleString('ar-SA')} نقطة`;\n    }\n    return `${points.toLocaleString('en-US')} points`;\n}\nfunction generateSlug(text) {\n    return text.toLowerCase().trim().replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\n    ;\n}\nfunction extractVideoId(url, type) {\n    if (type === 'youtube') {\n        const regex = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n        const match = url.match(regex);\n        return match ? match[1] : null;\n    }\n    if (type === 'vimeo') {\n        const regex = /(?:vimeo\\.com\\/)([0-9]+)/;\n        const match = url.match(regex);\n        return match ? match[1] : null;\n    }\n    return null;\n}\nfunction getVideoEmbedUrl(url, type) {\n    switch(type){\n        case 'youtube':\n            const youtubeId = extractVideoId(url, 'youtube');\n            return youtubeId ? `https://www.youtube.com/embed/${youtubeId}` : url;\n        case 'vimeo':\n            const vimeoId = extractVideoId(url, 'vimeo');\n            return vimeoId ? `https://player.vimeo.com/video/${vimeoId}` : url;\n        case 'drive':\n            // Convert Google Drive share link to embed link\n            const driveRegex = /\\/file\\/d\\/([a-zA-Z0-9-_]+)/;\n            const driveMatch = url.match(driveRegex);\n            return driveMatch ? `https://drive.google.com/file/d/${driveMatch[1]}/preview` : url;\n        case 'direct':\n        default:\n            return url;\n    }\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).trim() + '...';\n}\nfunction isRTL(locale) {\n    return locale === 'ar';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        user: null,\n        appUser: null,\n        isLoading: true,\n        setUser: (user)=>set({\n                user\n            }),\n        setAppUser: (appUser)=>set({\n                appUser\n            }),\n        setLoading: (isLoading)=>set({\n                isLoading\n            }),\n        logout: ()=>set({\n                user: null,\n                appUser: null\n            })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3RvcmUvYXV0aC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnQztBQWN6QixNQUFNQyxlQUFlRCwrQ0FBTUEsQ0FBWSxDQUFDRSxNQUFTO1FBQ3REQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTLENBQUNILE9BQVNELElBQUk7Z0JBQUVDO1lBQUs7UUFDOUJJLFlBQVksQ0FBQ0gsVUFBWUYsSUFBSTtnQkFBRUU7WUFBUTtRQUN2Q0ksWUFBWSxDQUFDSCxZQUFjSCxJQUFJO2dCQUFFRztZQUFVO1FBQzNDSSxRQUFRLElBQU1QLElBQUk7Z0JBQUVDLE1BQU07Z0JBQU1DLFNBQVM7WUFBSztJQUNoRCxJQUFHIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1vYW1lXFxEZXNrdG9wXFwzM1xcY291cnNlLXBsYXRmb3JtXFxzcmNcXHN0b3JlXFxhdXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZSB9IGZyb20gJ3p1c3RhbmQnXG5pbXBvcnQgeyBVc2VyIH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuaW1wb3J0IHsgVXNlciBhcyBBcHBVc2VyIH0gZnJvbSAnQC90eXBlcydcblxuaW50ZXJmYWNlIEF1dGhTdGF0ZSB7XG4gIHVzZXI6IFVzZXIgfCBudWxsXG4gIGFwcFVzZXI6IEFwcFVzZXIgfCBudWxsXG4gIGlzTG9hZGluZzogYm9vbGVhblxuICBzZXRVc2VyOiAodXNlcjogVXNlciB8IG51bGwpID0+IHZvaWRcbiAgc2V0QXBwVXNlcjogKGFwcFVzZXI6IEFwcFVzZXIgfCBudWxsKSA9PiB2b2lkXG4gIHNldExvYWRpbmc6IChsb2FkaW5nOiBib29sZWFuKSA9PiB2b2lkXG4gIGxvZ291dDogKCkgPT4gdm9pZFxufVxuXG5leHBvcnQgY29uc3QgdXNlQXV0aFN0b3JlID0gY3JlYXRlPEF1dGhTdGF0ZT4oKHNldCkgPT4gKHtcbiAgdXNlcjogbnVsbCxcbiAgYXBwVXNlcjogbnVsbCxcbiAgaXNMb2FkaW5nOiB0cnVlLFxuICBzZXRVc2VyOiAodXNlcikgPT4gc2V0KHsgdXNlciB9KSxcbiAgc2V0QXBwVXNlcjogKGFwcFVzZXIpID0+IHNldCh7IGFwcFVzZXIgfSksXG4gIHNldExvYWRpbmc6IChpc0xvYWRpbmcpID0+IHNldCh7IGlzTG9hZGluZyB9KSxcbiAgbG9nb3V0OiAoKSA9PiBzZXQoeyB1c2VyOiBudWxsLCBhcHBVc2VyOiBudWxsIH0pLFxufSkpXG4iXSwibmFtZXMiOlsiY3JlYXRlIiwidXNlQXV0aFN0b3JlIiwic2V0IiwidXNlciIsImFwcFVzZXIiLCJpc0xvYWRpbmciLCJzZXRVc2VyIiwic2V0QXBwVXNlciIsInNldExvYWRpbmciLCJsb2dvdXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/store/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/site.ts":
/*!***************************!*\
  !*** ./src/store/site.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSiteStore: () => (/* binding */ useSiteStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n\nconst useSiteStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        language: 'ar',\n        settings: [],\n        adsSettings: [],\n        isLoading: true,\n        setLanguage: (language)=>set({\n                language\n            }),\n        setSettings: (settings)=>set({\n                settings\n            }),\n        setAdsSettings: (adsSettings)=>set({\n                adsSettings\n            }),\n        setLoading: (isLoading)=>set({\n                isLoading\n            }),\n        getSetting: (key)=>{\n            const { settings } = get();\n            return settings.find((setting)=>setting.key === key);\n        },\n        getSettingValue: (key, language)=>{\n            const { settings, language: currentLanguage } = get();\n            const setting = settings.find((s)=>s.key === key);\n            if (!setting) return '';\n            const lang = language || currentLanguage;\n            return lang === 'ar' ? setting.value_ar || '' : setting.value_en || '';\n        },\n        getAdsByPosition: (position)=>{\n            const { adsSettings } = get();\n            return adsSettings.filter((ad)=>ad.position === position && ad.is_active);\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/site.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/lucide-react","vendor-chunks/whatwg-url","vendor-chunks/@radix-ui","vendor-chunks/tr46","vendor-chunks/zustand","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmoame%5CDesktop%5C33%5Ccourse-platform%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmoame%5CDesktop%5C33%5Ccourse-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();