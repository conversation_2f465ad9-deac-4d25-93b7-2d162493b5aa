'use client'

import Link from 'next/link'
import { BookOpen, Facebook, Twitter, Instagram, Youtube, Mail } from 'lucide-react'
import { useSiteStore } from '@/store/site'

interface FooterProps {
  locale: string
}

export function Footer({ locale }: FooterProps) {
  const { getSettingValue } = useSiteStore()
  
  const siteName = getSettingValue('site_name', locale as 'ar' | 'en')
  const siteDescription = getSettingValue('site_description', locale as 'ar' | 'en')
  const contactEmail = getSettingValue('contact_email')
  const facebookUrl = getSettingValue('social_facebook')
  const twitterUrl = getSettingValue('social_twitter')
  const instagramUrl = getSettingValue('social_instagram')
  const youtubeUrl = getSettingValue('social_youtube')

  const quickLinks = [
    {
      name: locale === 'ar' ? 'الرئيسية' : 'Home',
      href: `/${locale}`,
    },
    {
      name: locale === 'ar' ? 'الكورسات' : 'Courses',
      href: `/${locale}/courses`,
    },
    {
      name: locale === 'ar' ? 'التصنيفات' : 'Categories',
      href: `/${locale}/categories`,
    },
    {
      name: locale === 'ar' ? 'جمع النقاط' : 'Earn Points',
      href: `/${locale}/earn-points`,
    },
  ]

  const supportLinks = [
    {
      name: locale === 'ar' ? 'اتصل بنا' : 'Contact Us',
      href: `/${locale}/contact`,
    },
    {
      name: locale === 'ar' ? 'الأسئلة الشائعة' : 'FAQ',
      href: `/${locale}/faq`,
    },
    {
      name: locale === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy',
      href: `/${locale}/privacy`,
    },
    {
      name: locale === 'ar' ? 'شروط الاستخدام' : 'Terms of Service',
      href: `/${locale}/terms`,
    },
  ]

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, url: facebookUrl },
    { name: 'Twitter', icon: Twitter, url: twitterUrl },
    { name: 'Instagram', icon: Instagram, url: instagramUrl },
    { name: 'YouTube', icon: Youtube, url: youtubeUrl },
  ].filter(link => link.url)

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="flex items-center mb-4">
              <BookOpen className="h-8 w-8 text-blue-400" />
              <span className="ml-2 text-xl font-bold">
                {siteName || 'Course Platform'}
              </span>
            </div>
            <p className="text-gray-300 mb-4 leading-relaxed">
              {siteDescription || (locale === 'ar' 
                ? 'منصة تعليمية متقدمة لتعلم المهارات الجديدة'
                : 'Advanced learning platform for new skills'
              )}
            </p>
            {contactEmail && (
              <div className="flex items-center text-gray-300">
                <Mail className="h-4 w-4 mr-2" />
                <a href={`mailto:${contactEmail}`} className="hover:text-blue-400 transition-colors">
                  {contactEmail}
                </a>
              </div>
            )}
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">
              {locale === 'ar' ? 'روابط سريعة' : 'Quick Links'}
            </h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-blue-400 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">
              {locale === 'ar' ? 'الدعم' : 'Support'}
            </h3>
            <ul className="space-y-2">
              {supportLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-blue-400 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Social Media */}
          <div>
            <h3 className="text-lg font-semibold mb-4">
              {locale === 'ar' ? 'تابعنا' : 'Follow Us'}
            </h3>
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const Icon = social.icon
                return (
                  <a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-blue-400 transition-colors"
                    aria-label={social.name}
                  >
                    <Icon className="h-6 w-6" />
                  </a>
                )
              })}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {new Date().getFullYear()} {siteName || 'Course Platform'}. {' '}
              {locale === 'ar' ? 'جميع الحقوق محفوظة.' : 'All rights reserved.'}
            </p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <Link
                href={pathname.replace(`/${locale}`, '/ar')}
                className={`text-sm ${
                  locale === 'ar' ? 'text-blue-400' : 'text-gray-400 hover:text-blue-400'
                } transition-colors`}
              >
                العربية
              </Link>
              <span className="text-gray-600">|</span>
              <Link
                href={pathname.replace(`/${locale}`, '/en')}
                className={`text-sm ${
                  locale === 'en' ? 'text-blue-400' : 'text-gray-400 hover:text-blue-400'
                } transition-colors`}
              >
                English
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
