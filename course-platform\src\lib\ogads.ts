import { OGAdsResponse, OGAdsOffer } from '@/types'

export class OGAdsService {
  private apiKey: string
  private endpoint: string

  constructor() {
    this.apiKey = process.env.OGADS_API_KEY!
    this.endpoint = process.env.OGADS_API_ENDPOINT!
  }

  async getOffers(params: {
    ip: string
    userAgent: string
    ctype?: number
    max?: number
    country?: string
  }): Promise<OGAdsResponse> {
    try {
      const url = new URL(this.endpoint)
      
      // Add required parameters
      url.searchParams.append('ip', params.ip)
      url.searchParams.append('user_agent', params.userAgent)
      
      // Add optional parameters
      if (params.ctype) {
        url.searchParams.append('ctype', params.ctype.toString())
      }
      if (params.max) {
        url.searchParams.append('max', params.max.toString())
      }
      if (params.country) {
        url.searchParams.append('country', params.country)
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: OGAdsResponse = await response.json()
      return data
    } catch (error) {
      console.error('Error fetching OGAds offers:', error)
      return {
        success: false,
        error: 'Failed to fetch offers',
        offers: []
      }
    }
  }

  async handlePostback(params: {
    offerId: string
    offerName: string
    payout: number
    userId: string
    ip?: string
    datetime?: string
  }): Promise<boolean> {
    try {
      // This would typically be handled by your postback endpoint
      // For now, we'll just log the conversion
      console.log('OGAds conversion:', params)
      return true
    } catch (error) {
      console.error('Error handling OGAds postback:', error)
      return false
    }
  }

  calculatePoints(payout: number, pointsPerDollar: number = 100): number {
    return Math.floor(payout * pointsPerDollar)
  }

  getOfferTrackingUrl(offer: OGAdsOffer, userId: string): string {
    // Add user tracking parameters to the offer link
    const url = new URL(offer.link)
    url.searchParams.append('aff_sub4', userId)
    url.searchParams.append('aff_sub5', 'course-platform')
    return url.toString()
  }
}

export const ogadsService = new OGAdsService()
