-- Enable Row Level Security on all tables
ALTER TABLE admins ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE offers_completed ENABLE ROW LEVEL SECURITY;
ALTER TABLE site_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE ads_settings ENABLE ROW LEVEL SECURITY;

-- Admins table policies (only accessible via service role)
CREATE POLICY "Admins are only accessible via service role" ON admins
    FOR ALL USING (false);

-- Users table policies
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Categories table policies (public read)
CREATE POLICY "Categories are viewable by everyone" ON categories
    FOR SELECT USING (true);

-- Courses table policies (public read)
CREATE POLICY "Courses are viewable by everyone" ON courses
    FOR SELECT USING (true);

-- Course videos table policies (public read)
CREATE POLICY "Course videos are viewable by everyone" ON course_videos
    FOR SELECT USING (true);

-- User points table policies
CREATE POLICY "Users can view their own points" ON user_points
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own points" ON user_points
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Offers completed table policies
CREATE POLICY "Users can view their own completed offers" ON offers_completed
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own completed offers" ON offers_completed
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Site settings table policies (public read)
CREATE POLICY "Site settings are viewable by everyone" ON site_settings
    FOR SELECT USING (true);

-- Ads settings table policies (public read for active ads)
CREATE POLICY "Active ads are viewable by everyone" ON ads_settings
    FOR SELECT USING (is_active = true);

-- Function to update user points
CREATE OR REPLACE FUNCTION update_user_points(user_id UUID, points_to_add INTEGER, source_text TEXT, description_text TEXT)
RETURNS VOID AS $$
BEGIN
    -- Insert point transaction
    INSERT INTO user_points (user_id, points, source, description)
    VALUES (user_id, points_to_add, source_text, description_text);
    
    -- Update user total points
    UPDATE users 
    SET points = points + points_to_add,
        updated_at = NOW()
    WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has enough points for a course
CREATE OR REPLACE FUNCTION user_can_access_course(user_id UUID, course_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    user_points INTEGER;
    required_points INTEGER;
    is_free BOOLEAN;
BEGIN
    -- Get course info
    SELECT courses.required_points, courses.is_free 
    INTO required_points, is_free
    FROM courses 
    WHERE courses.id = course_id;
    
    -- If course is free, allow access
    IF is_free THEN
        RETURN true;
    END IF;
    
    -- Get user points
    SELECT users.points INTO user_points
    FROM users 
    WHERE users.id = user_id;
    
    -- Check if user has enough points
    RETURN user_points >= required_points;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
