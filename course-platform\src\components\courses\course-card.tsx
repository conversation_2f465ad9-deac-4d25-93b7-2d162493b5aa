'use client'

import Link from 'next/link'
import Image from 'next/image'
import { <PERSON>, <PERSON>, Star, Lock, Gift } from 'lucide-react'
import { <PERSON>, CardContent, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Course, Language } from '@/types'
import { formatPoints, truncateText } from '@/lib/utils'

interface CourseCardProps {
  course: Course
  locale: Language
  className?: string
}

export function CourseCard({ course, locale, className = '' }: CourseCardProps) {
  const title = locale === 'ar' ? course.title_ar : course.title_en
  const description = locale === 'ar' ? course.description_ar : course.description_en
  const slug = locale === 'ar' ? course.slug_ar : course.slug_en
  const categoryName = course.category 
    ? (locale === 'ar' ? course.category.name_ar : course.category.name_en)
    : ''

  const courseUrl = `/${locale}/courses/${slug}`
  const categoryUrl = course.category 
    ? `/${locale}/categories/${locale === 'ar' ? course.category.slug_ar : course.category.slug_en}`
    : '#'

  return (
    <Card className={`group hover:shadow-lg transition-all duration-300 ${className}`}>
      <div className="relative overflow-hidden rounded-t-lg">
        {course.image_url ? (
          <Image
            src={course.image_url}
            alt={title}
            width={400}
            height={200}
            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
            <span className="text-white text-lg font-semibold">{title.charAt(0)}</span>
          </div>
        )}
        
        {/* Course Type Badge */}
        <div className="absolute top-3 left-3">
          {course.is_free ? (
            <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              {locale === 'ar' ? 'مجاني' : 'Free'}
            </span>
          ) : (
            <span className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
              <Gift className="h-3 w-3 mr-1" />
              {formatPoints(course.required_points, locale)}
            </span>
          )}
        </div>

        {/* Category Badge */}
        {categoryName && (
          <div className="absolute top-3 right-3">
            <Link
              href={categoryUrl}
              className="bg-black bg-opacity-70 text-white px-2 py-1 rounded-full text-xs hover:bg-opacity-90 transition-colors"
            >
              {categoryName}
            </Link>
          </div>
        )}
      </div>

      <CardContent className="p-4">
        <div className="space-y-2">
          <h3 className="font-semibold text-lg line-clamp-2 group-hover:text-blue-600 transition-colors">
            <Link href={courseUrl}>
              {title}
            </Link>
          </h3>
          
          <p className="text-gray-600 text-sm line-clamp-3">
            {truncateText(description, 120)}
          </p>

          {/* Course Stats */}
          <div className="flex items-center justify-between text-xs text-gray-500 pt-2">
            <div className="flex items-center space-x-4">
              {course.videos && course.videos.length > 0 && (
                <div className="flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>
                    {course.videos.length} {locale === 'ar' ? 'فيديو' : 'videos'}
                  </span>
                </div>
              )}
              
              <div className="flex items-center">
                <Users className="h-3 w-3 mr-1" />
                <span>
                  {Math.floor(Math.random() * 1000) + 100} {locale === 'ar' ? 'طالب' : 'students'}
                </span>
              </div>
              
              <div className="flex items-center">
                <Star className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400" />
                <span>4.{Math.floor(Math.random() * 9) + 1}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0">
        <Link href={courseUrl} className="w-full">
          <Button className="w-full" variant={course.is_free ? "default" : "outline"}>
            {course.is_free ? (
              locale === 'ar' ? 'ابدأ التعلم' : 'Start Learning'
            ) : (
              <div className="flex items-center justify-center">
                <Lock className="h-4 w-4 mr-2" />
                {locale === 'ar' ? 'يتطلب نقاط' : 'Requires Points'}
              </div>
            )}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  )
}
