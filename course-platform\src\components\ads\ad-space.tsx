'use client'

import { useEffect, useState } from 'react'
import { useSiteStore } from '@/store/site'

interface AdSpaceProps {
  position: string
  className?: string
}

export function AdSpace({ position, className = '' }: AdSpaceProps) {
  const { getAdsByPosition } = useSiteStore()
  const [ads, setAds] = useState<any[]>([])

  useEffect(() => {
    const adsForPosition = getAdsByPosition(position)
    setAds(adsForPosition)
  }, [position, getAdsByPosition])

  if (ads.length === 0) {
    return null
  }

  return (
    <div className={`ad-space ad-position-${position} ${className}`}>
      {ads.map((ad) => (
        <div
          key={ad.id}
          className="ad-container"
          dangerouslySetInnerHTML={{ __html: ad.ad_code }}
        />
      ))}
    </div>
  )
}

// Google AdSense component
interface GoogleAdSenseProps {
  adSlot: string
  adFormat?: string
  fullWidthResponsive?: boolean
  className?: string
}

export function GoogleAdSense({ 
  adSlot, 
  adFormat = 'auto', 
  fullWidthResponsive = true,
  className = ''
}: GoogleAdSenseProps) {
  const { getSettingValue } = useSiteStore()
  const adsenseId = getSettingValue('google_adsense_id')

  useEffect(() => {
    if (typeof window !== 'undefined' && adsenseId) {
      try {
        // @ts-ignore
        (window.adsbygoogle = window.adsbygoogle || []).push({})
      } catch (error) {
        console.error('AdSense error:', error)
      }
    }
  }, [adsenseId])

  if (!adsenseId) {
    return null
  }

  return (
    <div className={`adsense-container ${className}`}>
      <ins
        className="adsbygoogle"
        style={{ display: 'block' }}
        data-ad-client={adsenseId}
        data-ad-slot={adSlot}
        data-ad-format={adFormat}
        data-full-width-responsive={fullWidthResponsive.toString()}
      />
    </div>
  )
}
