import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    
    // Extract postback parameters
    const offerId = searchParams.get('of_id')
    const offerName = searchParams.get('of_name')
    const amount = searchParams.get('amount')
    const userId = searchParams.get('user')
    const datetime = searchParams.get('datetime')
    const ip = searchParams.get('ip')

    // Validate required parameters
    if (!offerId || !offerName || !amount || !userId) {
      return NextResponse.json({ 
        error: 'Missing required parameters' 
      }, { status: 400 })
    }

    const payout = parseFloat(amount)
    if (isNaN(payout)) {
      return NextResponse.json({ 
        error: 'Invalid amount parameter' 
      }, { status: 400 })
    }

    // Create Supabase client with service role for admin operations
    const supabase = await createClient()

    // Check if this conversion already exists
    const { data: existingConversion } = await supabase
      .from('offers_completed')
      .select('id')
      .eq('user_id', userId)
      .eq('offer_id', offerId)
      .single()

    if (existingConversion) {
      return NextResponse.json({ 
        message: 'Conversion already processed' 
      }, { status: 200 })
    }

    // Calculate points (100 points per dollar by default)
    const pointsEarned = Math.floor(payout * 100)

    // Start a transaction
    const { error: transactionError } = await supabase.rpc('update_user_points', {
      user_id: userId,
      points_to_add: pointsEarned,
      source_text: 'ogads_offer',
      description_text: `Completed offer: ${offerName}`
    })

    if (transactionError) {
      console.error('Error updating user points:', transactionError)
      return NextResponse.json({ 
        error: 'Failed to update user points' 
      }, { status: 500 })
    }

    // Record the completed offer
    const { error: offerError } = await supabase
      .from('offers_completed')
      .insert({
        user_id: userId,
        offer_id: offerId,
        offer_name: offerName,
        points_earned: pointsEarned,
        payout: payout
      })

    if (offerError) {
      console.error('Error recording completed offer:', offerError)
      return NextResponse.json({ 
        error: 'Failed to record offer completion' 
      }, { status: 500 })
    }

    console.log(`OGAds conversion processed: User ${userId} earned ${pointsEarned} points for offer ${offerId}`)

    return NextResponse.json({ 
      success: true,
      message: 'Conversion processed successfully',
      pointsEarned
    })

  } catch (error) {
    console.error('Error in OGAds postback:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

// Handle POST requests as well
export async function POST(request: NextRequest) {
  return GET(request)
}
