'use client'

import Image from 'next/image'
import { ExternalLink, Gift, Smartphone, Monitor } from 'lucide-react'
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { OGAdsOffer, Language } from '@/types'
import { formatPoints, truncateText } from '@/lib/utils'

interface OfferCardProps {
  offer: OGAdsOffer & { trackingUrl?: string; estimatedPoints?: number }
  locale: Language
  onOfferClick?: (offer: OGAdsOffer) => void
  className?: string
}

export function OfferCard({ offer, locale, onOfferClick, className = '' }: OfferCardProps) {
  const handleOfferClick = () => {
    if (onOfferClick) {
      onOfferClick(offer)
    }
    
    // Open offer in new tab
    if (offer.trackingUrl) {
      window.open(offer.trackingUrl, '_blank', 'noopener,noreferrer')
    }
  }

  const getDeviceIcon = (device: string) => {
    if (device.toLowerCase().includes('android') || device.toLowerCase().includes('iphone')) {
      return <Smartphone className="h-4 w-4" />
    }
    return <Monitor className="h-4 w-4" />
  }

  const getCountryFlag = (country: string) => {
    // Simple country code to flag emoji mapping
    const countryFlags: { [key: string]: string } = {
      'US': '🇺🇸',
      'UK': '🇬🇧',
      'CA': '🇨🇦',
      'AU': '🇦🇺',
      'DE': '🇩🇪',
      'FR': '🇫🇷',
      'IT': '🇮🇹',
      'ES': '🇪🇸',
      'NL': '🇳🇱',
      'RU': '🇷🇺',
      'CZ': '🇨🇿',
    }
    
    const countries = country.split(',').map(c => c.trim())
    return countries.map(c => countryFlags[c] || '🌍').join(' ')
  }

  return (
    <Card className={`group hover:shadow-lg transition-all duration-300 ${className}`}>
      <CardContent className="p-4">
        <div className="flex space-x-4">
          {/* Offer Image */}
          <div className="flex-shrink-0">
            <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100">
              {offer.picture ? (
                <Image
                  src={offer.picture}
                  alt={offer.name_short}
                  width={64}
                  height={64}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <span className="text-white text-lg font-semibold">
                    {offer.name_short.charAt(0)}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Offer Details */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="font-semibold text-sm line-clamp-2 group-hover:text-blue-600 transition-colors">
                  {offer.name_short}
                </h3>
                
                <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                  {truncateText(offer.adcopy, 80)}
                </p>
              </div>

              {/* Points Badge */}
              <div className="flex-shrink-0 ml-2">
                <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <Gift className="h-3 w-3 mr-1" />
                  {offer.estimatedPoints 
                    ? formatPoints(offer.estimatedPoints, locale)
                    : `$${offer.payout}`
                  }
                </div>
              </div>
            </div>

            {/* Offer Meta */}
            <div className="flex items-center justify-between mt-3 text-xs text-gray-500">
              <div className="flex items-center space-x-3">
                <div className="flex items-center">
                  {getDeviceIcon(offer.device)}
                  <span className="ml-1">{offer.device}</span>
                </div>
                
                <div className="flex items-center">
                  <span className="mr-1">{getCountryFlag(offer.country)}</span>
                  <span>{offer.country}</span>
                </div>
              </div>

              <div className="text-right">
                <div className="text-green-600 font-medium">
                  ${offer.payout}
                </div>
                <div className="text-xs">
                  EPC: ${offer.epc}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0">
        <Button 
          onClick={handleOfferClick}
          className="w-full"
          size="sm"
        >
          <ExternalLink className="h-4 w-4 mr-2" />
          {locale === 'ar' ? 'ابدأ العرض' : 'Start Offer'}
        </Button>
      </CardFooter>
    </Card>
  )
}
