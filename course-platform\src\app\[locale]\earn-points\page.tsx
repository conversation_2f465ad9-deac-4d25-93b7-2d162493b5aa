import { Metadata } from 'next'
import { Suspense } from 'react'
import { Gift, TrendingUp, Clock, Shield } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AdSpace } from '@/components/ads/ad-space'
import { OffersList } from '@/components/offers/offers-list'

interface EarnPointsPageProps {
  params: { locale: string }
}

export async function generateMetadata({ params }: EarnPointsPageProps): Promise<Metadata> {
  const { locale } = params
  
  const title = locale === 'ar' ? 'جمع النقاط' : 'Earn Points'
  const description = locale === 'ar'
    ? 'اكسب نقاط من خلال إكمال العروض والمهام المختلفة'
    : 'Earn points by completing various offers and tasks'

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
    },
  }
}

export default function EarnPointsPage({ params }: EarnPointsPageProps) {
  const { locale } = params

  const features = [
    {
      icon: Gift,
      title: locale === 'ar' ? 'عروض متنوعة' : 'Diverse Offers',
      description: locale === 'ar' 
        ? 'مجموعة واسعة من العروض والمهام لكسب النقاط'
        : 'Wide variety of offers and tasks to earn points',
    },
    {
      icon: TrendingUp,
      title: locale === 'ar' ? 'نقاط فورية' : 'Instant Points',
      description: locale === 'ar'
        ? 'احصل على النقاط فور إكمال العروض بنجاح'
        : 'Get points instantly upon successful completion of offers',
    },
    {
      icon: Clock,
      title: locale === 'ar' ? 'متاح 24/7' : 'Available 24/7',
      description: locale === 'ar'
        ? 'عروض متاحة على مدار الساعة في جميع أنحاء العالم'
        : 'Offers available around the clock worldwide',
    },
    {
      icon: Shield,
      title: locale === 'ar' ? 'آمن وموثوق' : 'Safe & Reliable',
      description: locale === 'ar'
        ? 'جميع العروض من شركاء موثوقين ومعتمدين'
        : 'All offers from trusted and verified partners',
    },
  ]

  const howItWorks = [
    {
      step: '1',
      title: locale === 'ar' ? 'اختر عرضاً' : 'Choose an Offer',
      description: locale === 'ar'
        ? 'تصفح العروض المتاحة واختر ما يناسبك'
        : 'Browse available offers and choose what suits you',
    },
    {
      step: '2',
      title: locale === 'ar' ? 'أكمل المهمة' : 'Complete the Task',
      description: locale === 'ar'
        ? 'اتبع التعليمات وأكمل المهمة المطلوبة'
        : 'Follow instructions and complete the required task',
    },
    {
      step: '3',
      title: locale === 'ar' ? 'احصل على النقاط' : 'Get Points',
      description: locale === 'ar'
        ? 'احصل على النقاط فوراً في حسابك'
        : 'Get points instantly credited to your account',
    },
    {
      step: '4',
      title: locale === 'ar' ? 'استخدم النقاط' : 'Use Points',
      description: locale === 'ar'
        ? 'استخدم النقاط لفتح الكورسات المدفوعة'
        : 'Use points to unlock premium courses',
    },
  ]

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-600 to-blue-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {locale === 'ar' ? 'اكسب النقاط' : 'Earn Points'}
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-green-100 max-w-3xl mx-auto">
              {locale === 'ar'
                ? 'أكمل العروض والمهام البسيطة واكسب نقاط لفتح الكورسات المدفوعة'
                : 'Complete simple offers and tasks to earn points for unlocking premium courses'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <Card key={index} className="text-center">
                  <CardHeader>
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mx-auto mb-4">
                      <Icon className="h-8 w-8 text-green-600" />
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'ar' ? 'كيف يعمل النظام؟' : 'How It Works?'}
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              {locale === 'ar'
                ? 'خطوات بسيطة لكسب النقاط واستخدامها في فتح الكورسات'
                : 'Simple steps to earn points and use them to unlock courses'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {howItWorks.map((step, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 text-white rounded-full text-2xl font-bold mb-4">
                  {step.step}
                </div>
                <h3 className="text-lg font-semibold mb-2">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Content Top Ads */}
      <AdSpace position="content-top" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" />

      {/* Offers Section */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'ar' ? 'العروض المتاحة' : 'Available Offers'}
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              {locale === 'ar'
                ? 'اختر من بين العروض المتاحة وابدأ في كسب النقاط الآن'
                : 'Choose from available offers and start earning points now'
              }
            </p>
          </div>

          <Suspense fallback={
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">
                {locale === 'ar' ? 'جاري تحميل العروض...' : 'Loading offers...'}
              </p>
            </div>
          }>
            <OffersList locale={locale} />
          </Suspense>
        </div>
      </section>

      {/* Content Bottom Ads */}
      <AdSpace position="content-bottom" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" />
    </div>
  )
}
