import { Metadata } from 'next'
import { RegisterForm } from '@/components/auth/register-form'

interface RegisterPageProps {
  params: { locale: string }
}

export async function generateMetadata({ params }: RegisterPageProps): Promise<Metadata> {
  const { locale } = params
  
  const title = locale === 'ar' ? 'إنشاء حساب' : 'Register'
  const description = locale === 'ar'
    ? 'أنشئ حساباً جديداً للوصول إلى الكورسات وكسب النقاط'
    : 'Create a new account to access courses and earn points'

  return {
    title,
    description,
  }
}

export default function RegisterPage({ params }: RegisterPageProps) {
  const { locale } = params

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {locale === 'ar' ? 'إنشاء حساب جديد' : 'Create your account'}
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {locale === 'ar' ? 'أو' : 'Or'}{' '}
            <a
              href={`/${locale}/login`}
              className="font-medium text-blue-600 hover:text-blue-500"
            >
              {locale === 'ar' ? 'تسجيل الدخول إلى حساب موجود' : 'sign in to your existing account'}
            </a>
          </p>
        </div>
        <RegisterForm locale={locale} />
      </div>
    </div>
  )
}
