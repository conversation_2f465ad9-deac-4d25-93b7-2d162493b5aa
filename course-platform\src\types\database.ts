export interface Database {
  public: {
    Tables: {
      admins: {
        Row: {
          id: string
          username: string
          password_hash: string
          email: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          username: string
          password_hash: string
          email: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          username?: string
          password_hash?: string
          email?: string
          created_at?: string
          updated_at?: string
        }
      }
      users: {
        Row: {
          id: string
          email: string
          username: string
          points: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          username: string
          points?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          username?: string
          points?: number
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name_ar: string
          name_en: string
          slug_ar: string
          slug_en: string
          description_ar: string | null
          description_en: string | null
          image_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name_ar: string
          name_en: string
          slug_ar: string
          slug_en: string
          description_ar?: string | null
          description_en?: string | null
          image_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name_ar?: string
          name_en?: string
          slug_ar?: string
          slug_en?: string
          description_ar?: string | null
          description_en?: string | null
          image_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      courses: {
        Row: {
          id: string
          title_ar: string
          title_en: string
          slug_ar: string
          slug_en: string
          description_ar: string
          description_en: string
          content_ar: string
          content_en: string
          image_url: string | null
          category_id: string
          is_free: boolean
          required_points: number
          meta_title_ar: string | null
          meta_title_en: string | null
          meta_description_ar: string | null
          meta_description_en: string | null
          keywords_ar: string | null
          keywords_en: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title_ar: string
          title_en: string
          slug_ar: string
          slug_en: string
          description_ar: string
          description_en: string
          content_ar: string
          content_en: string
          image_url?: string | null
          category_id: string
          is_free?: boolean
          required_points?: number
          meta_title_ar?: string | null
          meta_title_en?: string | null
          meta_description_ar?: string | null
          meta_description_en?: string | null
          keywords_ar?: string | null
          keywords_en?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title_ar?: string
          title_en?: string
          slug_ar?: string
          slug_en?: string
          description_ar?: string
          description_en?: string
          content_ar?: string
          content_en?: string
          image_url?: string | null
          category_id?: string
          is_free?: boolean
          required_points?: number
          meta_title_ar?: string | null
          meta_title_en?: string | null
          meta_description_ar?: string | null
          meta_description_en?: string | null
          keywords_ar?: string | null
          keywords_en?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      course_videos: {
        Row: {
          id: string
          course_id: string
          title_ar: string
          title_en: string
          video_url: string
          video_type: 'youtube' | 'vimeo' | 'drive' | 'direct'
          duration: number | null
          order_index: number
          is_free: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          course_id: string
          title_ar: string
          title_en: string
          video_url: string
          video_type: 'youtube' | 'vimeo' | 'drive' | 'direct'
          duration?: number | null
          order_index: number
          is_free?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          course_id?: string
          title_ar?: string
          title_en?: string
          video_url?: string
          video_type?: 'youtube' | 'vimeo' | 'drive' | 'direct'
          duration?: number | null
          order_index?: number
          is_free?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      user_points: {
        Row: {
          id: string
          user_id: string
          points: number
          source: string
          description: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          points: number
          source: string
          description: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          points?: number
          source?: string
          description?: string
          created_at?: string
        }
      }
      offers_completed: {
        Row: {
          id: string
          user_id: string
          offer_id: string
          offer_name: string
          points_earned: number
          payout: number
          completed_at: string
        }
        Insert: {
          id?: string
          user_id: string
          offer_id: string
          offer_name: string
          points_earned: number
          payout: number
          completed_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          offer_id?: string
          offer_name?: string
          points_earned?: number
          payout?: number
          completed_at?: string
        }
      }
      site_settings: {
        Row: {
          id: string
          key: string
          value_ar: string | null
          value_en: string | null
          type: 'text' | 'textarea' | 'number' | 'boolean' | 'json'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          key: string
          value_ar?: string | null
          value_en?: string | null
          type: 'text' | 'textarea' | 'number' | 'boolean' | 'json'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          key?: string
          value_ar?: string | null
          value_en?: string | null
          type?: 'text' | 'textarea' | 'number' | 'boolean' | 'json'
          created_at?: string
          updated_at?: string
        }
      }
      ads_settings: {
        Row: {
          id: string
          name: string
          position: string
          ad_code: string
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          position: string
          ad_code: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          position?: string
          ad_code?: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
