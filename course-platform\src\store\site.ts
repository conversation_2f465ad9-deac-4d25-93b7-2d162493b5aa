import { create } from 'zustand'
import { SiteSetting, AdsSetting, Language } from '@/types'

interface SiteState {
  language: Language
  settings: SiteSetting[]
  adsSettings: AdsSetting[]
  isLoading: boolean
  setLanguage: (language: Language) => void
  setSettings: (settings: SiteSetting[]) => void
  setAdsSettings: (adsSettings: AdsSetting[]) => void
  setLoading: (loading: boolean) => void
  getSetting: (key: string) => SiteSetting | undefined
  getSettingValue: (key: string, language?: Language) => string
  getAdsByPosition: (position: string) => AdsSetting[]
}

export const useSiteStore = create<SiteState>((set, get) => ({
  language: 'ar',
  settings: [],
  adsSettings: [],
  isLoading: true,
  setLanguage: (language) => set({ language }),
  setSettings: (settings) => set({ settings }),
  setAdsSettings: (adsSettings) => set({ adsSettings }),
  setLoading: (isLoading) => set({ isLoading }),
  getSetting: (key) => {
    const { settings } = get()
    return settings.find(setting => setting.key === key)
  },
  getSettingValue: (key, language) => {
    const { settings, language: currentLanguage } = get()
    const setting = settings.find(s => s.key === key)
    if (!setting) return ''
    
    const lang = language || currentLanguage
    return lang === 'ar' ? (setting.value_ar || '') : (setting.value_en || '')
  },
  getAdsByPosition: (position) => {
    const { adsSettings } = get()
    return adsSettings.filter(ad => ad.position === position && ad.is_active)
  },
}))
